#!/usr/bin/env node

/**
 * 自动修复 lint 问题的脚本
 * 使用方法: node scripts/lint-fix.js [文件路径]
 */

import { execSync } from "child_process";

function runCommand(command, options = {}) {
  try {
    const result = execSync(command, {
      encoding: "utf8",
      stdio: "pipe",
      ...options,
    });
    return { success: true, output: result };
  } catch (error) {
    return { success: false, error: error.message, output: error.stdout || "" };
  }
}

function main() {
  const args = process.argv.slice(2);
  const targetFile = args[0] || ".";

  console.log("🔧 开始自动修复 lint 问题...\n");

  // 1. 运行 ESLint 自动修复
  console.log("📝 运行 ESLint 自动修复...");
  const eslintResult = runCommand(`npx eslint "${targetFile}" --fix --quiet`);

  if (eslintResult.success) {
    console.log("✅ ESLint 自动修复完成");
  } else {
    console.log("⚠️  ESLint 修复过程中有警告或错误:");
    console.log(eslintResult.output);
  }

  // 2. 运行 Prettier 格式化
  console.log("\n🎨 运行 Prettier 格式化...");
  const prettierResult = runCommand(`npx prettier --write "${targetFile}"`);

  if (prettierResult.success) {
    console.log("✅ Prettier 格式化完成");
    console.log(prettierResult.output);
  } else {
    console.log("❌ Prettier 格式化失败:");
    console.log(prettierResult.error);
  }

  // 3. 再次运行 ESLint 检查剩余问题
  console.log("\n🔍 检查剩余的 lint 问题...");
  const finalCheckResult = runCommand(`npx eslint "${targetFile}" --quiet`);

  if (finalCheckResult.success && !finalCheckResult.output.trim()) {
    console.log("🎉 所有 lint 问题已修复！");
  } else {
    console.log("📋 剩余的 lint 问题:");
    console.log(finalCheckResult.output || "无法自动修复的问题，请手动处理");
  }

  console.log("\n✨ 自动修复完成！");
}

// 如果直接运行此脚本，执行 main 函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main, runCommand };

#!/usr/bin/env node

/**
 * 重置 i18n-ally 插件缓存的脚本
 * 用于解决插件无法正确识别翻译键的问题
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

function removeDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
    console.log(`✅ 已删除: ${dirPath}`);
  } else {
    console.log(`ℹ️  目录不存在: ${dirPath}`);
  }
}

function createTempFile(filePath, content) {
  fs.writeFileSync(filePath, content);
  console.log(`✅ 已创建临时文件: ${filePath}`);
}

function removeTempFile(filePath) {
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    console.log(`✅ 已删除临时文件: ${filePath}`);
  }
}

function main() {
  console.log('🔄 重置 i18n-ally 插件缓存...\n');
  
  // 1. 清理可能的缓存目录
  const cacheDirectories = [
    '.vscode/.i18n-ally',
    'node_modules/.cache/i18n-ally',
    '.next/cache/i18n-ally',
    'apps/web/.next/cache/i18n-ally'
  ];
  
  console.log('🗑️  清理缓存目录:');
  cacheDirectories.forEach(removeDirectory);
  
  // 2. 创建临时配置文件来触发重新加载
  console.log('\n🔧 创建临时配置文件:');
  const tempConfigPath = '.vscode/i18n-ally-temp.json';
  const tempConfig = {
    "timestamp": new Date().toISOString(),
    "action": "force-reload"
  };
  
  createTempFile(tempConfigPath, JSON.stringify(tempConfig, null, 2));
  
  // 3. 等待一秒后删除临时文件
  setTimeout(() => {
    removeTempFile(tempConfigPath);
    
    console.log('\n📋 建议的后续步骤:');
    console.log('1. 重启 VSCode');
    console.log('2. 打开命令面板 (Ctrl+Shift+P / Cmd+Shift+P)');
    console.log('3. 运行 "i18n Ally: Reload"');
    console.log('4. 运行 "Developer: Reload Window"');
    console.log('5. 检查 i18n-ally 状态栏是否显示正确的语言数量');
    
    console.log('\n🔍 调试信息:');
    console.log('- 翻译文件路径: apps/web/messages/');
    console.log('- 支持的语言: zh, en');
    console.log('- 命名空间: 启用');
    console.log('- 框架: next-intl');
    
    console.log('\n✨ 重置完成！');
  }, 1000);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main };

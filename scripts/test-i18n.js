#!/usr/bin/env node

/**
 * 测试 i18n 配置的脚本
 * 验证翻译键是否存在
 */

import fs from 'fs';
import path from 'path';

function loadTranslations(locale) {
  const filePath = path.join(process.cwd(), 'apps/web/messages', `${locale}.json`);
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Failed to load ${locale} translations:`, error.message);
    return {};
  }
}

function getNestedValue(obj, keyPath) {
  return keyPath.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

function testTranslationKey(translations, namespace, key) {
  const fullKey = namespace ? `${namespace}.${key}` : key;
  const value = getNestedValue(translations, fullKey);
  
  return {
    key: fullKey,
    exists: value !== undefined,
    value: value
  };
}

function main() {
  console.log('🔍 测试 i18n 配置...\n');
  
  const zhTranslations = loadTranslations('zh');
  const enTranslations = loadTranslations('en');
  
  // 测试一些常用的翻译键
  const testKeys = [
    { namespace: 'knowledgeBase.details', key: 'title' },
    { namespace: 'knowledgeBase.details', key: 'successTitle' },
    { namespace: 'knowledgeBase.details', key: 'errorTitle' },
    { namespace: 'knowledgeBase.details', key: 'fileName' },
    { namespace: 'knowledgeBase.details', key: 'actions' },
    { namespace: 'knowledgeBase.details', key: 'notSet' },
    { namespace: 'knowledgeBase.status', key: 'parseSuccess' },
    { namespace: 'knowledgeBase.status', key: 'parsing' },
  ];
  
  console.log('📋 测试翻译键存在性:\n');
  
  let allPassed = true;
  
  testKeys.forEach(({ namespace, key }) => {
    const zhResult = testTranslationKey(zhTranslations, namespace, key);
    const enResult = testTranslationKey(enTranslations, namespace, key);
    
    const zhStatus = zhResult.exists ? '✅' : '❌';
    const enStatus = enResult.exists ? '✅' : '❌';
    
    console.log(`${zhStatus} ${enStatus} ${zhResult.key}`);
    
    if (zhResult.exists) {
      console.log(`   zh: "${zhResult.value}"`);
    }
    if (enResult.exists) {
      console.log(`   en: "${enResult.value}"`);
    }
    
    if (!zhResult.exists || !enResult.exists) {
      allPassed = false;
    }
    
    console.log('');
  });
  
  // 检查命名空间结构
  console.log('🏗️  检查命名空间结构:\n');
  
  const namespaces = ['knowledgeBase', 'agent', 'dataset', 'home'];
  
  namespaces.forEach(ns => {
    const zhNs = zhTranslations[ns];
    const enNs = enTranslations[ns];
    
    const zhExists = zhNs !== undefined;
    const enExists = enNs !== undefined;
    
    console.log(`${zhExists ? '✅' : '❌'} ${enExists ? '✅' : '❌'} ${ns}`);
    
    if (zhExists && typeof zhNs === 'object') {
      const subKeys = Object.keys(zhNs);
      console.log(`   子键数量: ${subKeys.length}`);
      console.log(`   子键: ${subKeys.slice(0, 5).join(', ')}${subKeys.length > 5 ? '...' : ''}`);
    }
    
    console.log('');
  });
  
  if (allPassed) {
    console.log('🎉 所有测试通过！');
  } else {
    console.log('⚠️  有些翻译键缺失，请检查翻译文件。');
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main, testTranslationKey, loadTranslations };

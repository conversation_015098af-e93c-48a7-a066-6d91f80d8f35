# Lint 和 Prettier 配置指南

本项目已配置了完整的代码质量和格式化工具链，包括 ESLint、Prettier 和自动修复功能。

## 🛠️ 工具配置

### ESLint 配置
- **配置文件**: `packages/eslint-config/index.js`
- **规则集**: Next.js、React、TypeScript、Prettier 兼容
- **插件**: 
  - `eslint-plugin-react`
  - `eslint-plugin-react-hooks`
  - `eslint-plugin-unused-imports`
  - `eslint-plugin-unicorn`

### Prettier 配置
- **配置文件**: `.prettierrc`
- **打印宽度**: 100 字符
- **插件**: `prettier-plugin-tailwindcss`

### VSCode 配置
- **配置文件**: `.vscode/settings.json`
- **保存时自动修复**: 已启用
- **格式化器**: Prettier

## 📝 可用脚本

### 基础命令
```bash
# 检查 lint 问题（不修复）
pnpm lint:check

# 自动修复 lint 问题
pnpm lint

# 格式化所有文件
pnpm format

# 检查格式化（不修复）
pnpm format:check

# 自动修复单个文件或目录（推荐）
pnpm lint:fix [文件路径]
```

### 高级用法
```bash
# 修复特定文件
pnpm lint:fix "apps/web/components/MyComponent.tsx"

# 修复整个目录
pnpm lint:fix "apps/web/components/"

# 修复所有文件
pnpm lint:fix
```

## 🔧 自动修复功能

### 保存时自动修复
当你保存文件时，VSCode 会自动：
1. 运行 ESLint 自动修复
2. 整理导入语句
3. 运行 Prettier 格式化

### 手动修复
使用我们的自定义脚本 `scripts/lint-fix.js`：
```bash
node scripts/lint-fix.js [文件路径]
```

这个脚本会：
1. 运行 ESLint 自动修复
2. 运行 Prettier 格式化
3. 显示剩余的问题

## 🎯 VSCode 任务

在 VSCode 中按 `Ctrl+Shift+P`（Mac: `Cmd+Shift+P`），然后输入 "Tasks: Run Task"，可以运行：

- **ESLint: Fix All Auto-fixable Problems** - 修复所有可自动修复的 ESLint 问题
- **Prettier: Format All Files** - 格式化所有文件
- **Lint Fix: Auto-fix Current File** - 自动修复当前文件
- **Lint Fix: Auto-fix All Files** - 自动修复所有文件

## 📋 代码质量规则

### ESLint 规则亮点
- **未使用导入清理**: 自动移除未使用的导入
- **React Hooks 规则**: 确保 Hooks 正确使用
- **TypeScript 规则**: 类型安全检查
- **代码风格**: 统一的代码风格

### Prettier 规则亮点
- **打印宽度**: 100 字符
- **缩进**: 2 空格
- **分号**: 总是添加
- **引号**: 双引号
- **尾随逗号**: ES5 兼容

## 🚀 最佳实践

### 开发流程
1. 编写代码
2. 保存文件（自动修复）
3. 提交前运行 `pnpm lint:check` 确保无问题

### 团队协作
- 所有团队成员使用相同的 VSCode 配置
- 提交前确保代码通过 lint 检查
- 使用 `pnpm lint:fix` 快速修复问题

### 性能优化
- 只对修改的文件运行 lint
- 使用 `--quiet` 标志减少输出
- 配置了合理的忽略规则

## 🔍 故障排除

### 常见问题

**问题**: ESLint 报告类型错误
**解决**: 确保 TypeScript 配置正确，运行 `pnpm lint:fix`

**问题**: Prettier 和 ESLint 冲突
**解决**: 已配置 `eslint-config-prettier` 避免冲突

**问题**: 保存时不自动修复
**解决**: 检查 VSCode 设置，确保启用了 `editor.codeActionsOnSave`

### 调试命令
```bash
# 查看 ESLint 配置
npx eslint --print-config file.ts

# 查看 Prettier 配置
npx prettier --find-config-path file.ts

# 详细的 lint 输出
npx eslint . --debug
```

## 📚 相关文档

- [ESLint 官方文档](https://eslint.org/)
- [Prettier 官方文档](https://prettier.io/)
- [Next.js ESLint 配置](https://nextjs.org/docs/basic-features/eslint)
- [TypeScript ESLint 规则](https://typescript-eslint.io/rules/)

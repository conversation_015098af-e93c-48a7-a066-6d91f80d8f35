---
description:
globs:
alwaysApply: false
---
---
description: Frontend components standards (React 19, Tailwind, shadcn/radix)
globs: ["apps/management/**/*.{ts,tsx}","apps/web/**/*.{ts,tsx}", "packages/ui/src/**/*.{ts,tsx}"]
alwaysApply: false
agentRequested: true
---

- Use **React 19** functional components with TypeScript.
- Component styles must use **TailwindCSS**:
  - Use `class:` shorthand for conditional classes
  - Avoid inline styles
  - Group related classes logically
- UI Components:
  - Use `shadcn/radix` primitives for base components
  - Create reusable components in `packages/ui`
  - Implement proper prop types and documentation
- Accessibility requirements:
  - Semantic HTML elements
  - ARIA attributes where needed
  - Keyboard navigation support
  - Focus management
  - Screen reader compatibility
- Code organization:
  - One component per file
  - Clear component hierarchy
  - Proper prop drilling or context usage
  - Memoization when necessary
- Error handling and loading states must be implemented

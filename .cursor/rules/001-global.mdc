---
description:
globs:
alwaysApply: false
---
---
description: Global AI behavior: enforce TS, Tailwind, accessibility
globs: ["**/*"]
alwaysApply: true
---

- Always use **TypeScript** (no plain JS) with strict type checking.
- Use **TailwindCSS** classes for styling; avoid custom CSS.
- Prefer **early returns** for better readability and maintainability.
- Event handlers must use `handle...` prefix (e.g., `handleClick`, `handleKeyDown`).
- All interactive elements must have:
  - `aria-label` or `aria-labelledby`
  - `tabindex="0"`
  - `onKeyDown` for keyboard interaction
  - Proper focus management
- Use `const` for function declarations (e.g., `const handleClick = () => {}`).
- Define TypeScript interfaces/types for all component props and state.
- Follow DRY principles and maintain consistent code formatting.

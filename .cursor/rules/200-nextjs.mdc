---
description:
globs:
alwaysApply: false
---
---
description: Next.js 15 + React 19 page and layout conventions
globs: ["apps/management/**/*.{ts,tsx}", "apps/web/**/*.{ts,tsx}"]
alwaysApply: false
agentRequested: true
---

- App Router Structure:
  - Use `page.tsx` for routes
  - Use `layout.tsx` for shared layouts
  - Use `loading.tsx` for loading states
  - Use `error.tsx` for error boundaries
  - Use `not-found.tsx` for 404 pages
- Client/Server Components:
  - Mark client components with `'use client'` directive
  - Keep server components as default
  - Use proper data fetching methods (Server Actions, Route Handlers)
- File Organization:
  - Directories: kebab-case (e.g., `user-profile`)
  - Files: PascalCase (e.g., `UserProfile.tsx`)
  - Group related components in feature folders
- Performance:
  - Implement proper caching strategies
  - Use Image component for optimized images
  - Implement proper metadata
  - Use proper loading strategies
- Shared Components:
  - Import UI components from `packages/ui`
  - Create shared layouts in `packages/ui/layouts`
  - Use proper component composition

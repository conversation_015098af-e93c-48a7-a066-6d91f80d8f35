---
description: 
globs: 
alwaysApply: false
---
---
description: UI package structure and export conventions
globs: ["packages/ui/src/**/*.{ts,tsx}"]
alwaysApply: false
agentRequested: true
---

- Build components in **packages/ui** as isolated, reusable primitives.
- Expose components via `export * from './ComponentName'` in `packages/ui/src/index.ts`.
- Each component file begins with `'use client'` if it contains client logic.
- Use `TailwindCSS` for styling, prefer utility-first approach.
- Add `aria-label`, `tabindex="0"`, and keyboard handlers for interactive parts.

# i18n-ally 自定义框架配置
# 用于支持 next-intl 的命名空间功能

languageIds:
  - javascript
  - typescript
  - javascriptreact
  - typescriptreact

# 使用模式匹配
usageMatchRegex:
  # 匹配 useTranslations('namespace').t('key')
  - "useTranslations\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\).*?\\bt\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)"
  # 匹配 t('key') 在 useTranslations 上下文中
  - "\\bt\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)"

# 重写规则
rewriteRules:
  - from: "useTranslations\\('([^']+)'\\)\\.t\\('([^']+)'\\)"
    to: "$1.$2"

# 检测模式
detectionPatterns:
  - "useTranslations\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)"
  - "getTranslations\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)"

# 命名空间支持
namespaceDelimiter: "."
supportNamespace: true

# 键值提取模式
keyMatchReg: "['\"`]([\\w\\d\\-_\\.]+)['\"`]"

# 排除模式
excludePatterns:
  - "node_modules/**"
  - ".next/**"
  - "dist/**"
  - "build/**"

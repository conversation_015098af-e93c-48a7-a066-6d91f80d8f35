{"version": "2.0.0", "tasks": [{"label": "ESLint: Fix All Auto-fixable Problems", "type": "shell", "command": "npx", "args": ["eslint", "${workspaceFolder}", "--fix"], "group": "build", "presentation": {"echo": true, "reveal": "never", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$eslint-stylish"]}, {"label": "Prettier: Format All Files", "type": "shell", "command": "npx", "args": ["prettier", "--write", "**/*.{ts,tsx,js,jsx,json,md}"], "group": "build", "presentation": {"echo": true, "reveal": "never", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "Lint Fix: Auto-fix Current File", "type": "shell", "command": "node", "args": ["scripts/lint-fix.js", "${file}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "Lint Fix: Auto-fix All Files", "type": "shell", "command": "node", "args": ["scripts/lint-fix.js", "."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}]}
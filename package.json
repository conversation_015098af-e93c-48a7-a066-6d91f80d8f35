{"name": "ragtop-web", "version": "0.0.1", "type": "module", "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "eslint . --fix", "lint:check": "eslint .", "lint:fix": "node scripts/lint-fix.js", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,md}\"", "i18n:test": "node scripts/test-i18n.js", "i18n:reset": "node scripts/reset-i18n-ally.js"}, "devDependencies": {"@ragtop-web/eslint-config": "workspace:*", "@ragtop-web/typescript-config": "workspace:*", "eslint": "9.29.0", "prettier": "^3.5.1", "turbo": "^2.4.2", "typescript": "5.7.3"}, "packageManager": "pnpm@10.4.1", "engines": {"node": ">=20"}, "dependencies": {"@hookform/resolvers": "5.0.1", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-scroll-area": "1.2.8", "@tanstack/react-query": "5.76.1", "canvas": "2.11.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jotai": "^2.7.0", "lucide-react": "^0.525.0", "prettier-plugin-tailwindcss": "0.6.13", "radix-ui": "1.4.2", "react-hook-form": "7.56.3", "tailwind-merge": "^3.0.1", "tailwindcss": "^4.0.8", "zod": "^3.24.2"}}
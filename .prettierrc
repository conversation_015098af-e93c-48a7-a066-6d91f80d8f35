{"printWidth": 100, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": false, "quoteProps": "as-needed", "jsxSingleQuote": false, "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "proseWrap": "preserve", "insertPragma": false, "requirePragma": false, "plugins": ["prettier-plugin-tailwindcss"], "overrides": [{"files": "*.{js,jsx,ts,tsx}", "options": {"parser": "typescript"}}, {"files": "*.json", "options": {"parser": "json", "printWidth": 120}}, {"files": "*.md", "options": {"parser": "markdown", "printWidth": 80, "proseWrap": "always"}}]}
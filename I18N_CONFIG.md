# i18n-ally 插件配置说明

## 概述

本项目已配置 i18n-ally 插件来支持国际化开发，包括：

- 自动检测硬编码的中文文本
- 提供翻译键的自动补全
- 支持 next-intl 框架
- 多语言文件管理

## 配置详情

### 1. 语言支持

- **源语言**: 中文 (zh)
- **目标语言**: 英文 (en)
- **默认语言**: 中文 (zh)

### 2. 文件结构

```
apps/
├── web/
│   ├── messages/
│   │   ├── zh.json    # 中文翻译文件
│   │   └── en.json    # 英文翻译文件
│   └── i18n.ts        # i18n 配置文件
└── management/
    ├── messages/
    │   ├── zh.json    # 中文翻译文件
    │   └── en.json    # 英文翻译文件
    └── i18n.ts        # i18n 配置文件
```

### 3. 主要功能

#### 硬编码检测

- 自动检测代码中的中文硬编码文本
- 支持 TypeScript、JavaScript、JSX、TSX 文件
- 忽略注释、console 语句等

#### 翻译键管理

- 嵌套键结构支持 (如: `agent.common.loading`)
- 自动补全翻译键
- 键值对验证

#### 文件同步

- 自动同步不同语言文件中的键
- 保持键结构一致

## 使用方法

### 1. 使用翻译键

```tsx
import { useTranslations } from "next-intl";

export default function MyComponent() {
  const t = useTranslations("agent");

  return (
    <div>
      <p>{t("common.loading")}</p>
      <p>{t("actions.edit")}</p>
    </div>
  );
}
```

### 2. 检测硬编码

i18n-ally 会自动检测以下模式的中文文本：

- 字符串字面量: `"这是中文文本"`
- 模板字符串: `` `这是中文文本` ``
- JSX 文本: `<p>这是中文文本</p>`

### 3. 提取硬编码

1. 选中硬编码的中文文本
2. 右键选择 "i18n-ally: Extract Text"
3. 输入翻译键名
4. 自动生成翻译键并添加到语言文件中

## 配置项说明

### VSCode 设置

- `i18n-ally.localesPaths`: 语言文件路径
- `i18n-ally.sourceLanguage`: 源语言
- `i18n-ally.displayLanguage`: 显示语言
- `i18n-ally.enabledFrameworks`: 启用的框架 (next-intl)
- `i18n-ally.extract.autoDetectHardcodedString`: 自动检测硬编码
- `i18n-ally.extract.hardcodedStringPatterns`: 硬编码检测模式

### 忽略规则

以下内容会被忽略：

- 注释中的中文
- console 语句中的中文
- import/export 语句中的中文
- 变量声明中的中文

## 注意事项

1. **重启 VSCode**: 修改配置后需要重启 VSCode 才能生效
2. **文件路径**: 确保语言文件路径配置正确
3. **键名规范**: 使用点分隔的嵌套键名 (如: `agent.common.loading`)
4. **同步更新**: 添加新键时确保所有语言文件都同步更新

## 故障排除

### 问题: "not found in source with language tag 'zh'"

**解决方案**:

1. 检查 `.vscode/settings.json` 中的 `i18n-ally.localesPaths` 配置
2. 确保语言文件存在且路径正确
3. 重启 VSCode

### 问题: 硬编码中文无法检测

**解决方案**:

1. 检查 `i18n-ally.extract.autoDetectHardcodedString` 是否启用
2. 确认文件类型在支持列表中
3. 检查忽略规则是否过于严格

### 问题: 翻译键无法自动补全

**解决方案**:

1. 确认 `i18n-ally.enabledFrameworks` 包含 "next-intl"
2. 检查语言文件格式是否正确
3. 重启 VSCode 或重新加载窗口

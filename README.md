# shadcn/ui monorepo template

This template is for creating a monorepo with shadcn/ui.

## Usage

```bash
pnpm dlx shadcn@latest init
```

## Adding components

To add components to your app, run the following command at the root of your
`web` app:

```bash
pnpm dlx shadcn@latest add button -c apps/web
```

This will place the ui components in the `packages/ui/src/components` directory.

## Tailwind

Your `tailwind.config.ts` and `globals.css` are already set up to use the
components from the `ui` package.

## Using components

To use the components in your app, import them from the `ui` package.

```tsx
import { Button } from "@ragtop-web/ui/components/button";
```

# RAGTop Web

## 环境变量配置

### Web应用环境变量

复制 `apps/web/env.example` 到 `apps/web/.env.local` 并根据需要修改：

```bash
# API配置
NEXT_PUBLIC_API_PREFIX=/api/v1/portal-ragtop
API_BASE_URL=

# 功能开关
SHOW_NL2CODE=true

# 上传配置
UPLOAD_MODE=

# 开发环境配置
NODE_ENV=development
```

### Management应用环境变量

复制 `apps/management/env.example` 到 `apps/management/.env.local`
并根据需要修改：

```bash
# API配置
NEXT_PUBLIC_API_PREFIX=/api/v1/portal-ragtop-admin
API_BASE_URL=

# 开发环境配置
NODE_ENV=development
```

### 环境变量说明

- `NEXT_PUBLIC_API_PREFIX`: API前缀路径，用于构建完整的API端点
- `API_BASE_URL`: API基础URL，用于外部API调用
- `SHOW_NL2CODE`: 是否显示NL2Code功能，值为 'true' 或 'false'
- `UPLOAD_MODE`: 文件上传模式配置
- `NODE_ENV`: 运行环境，通常为 'development'、'production' 或 'test'

## 开发

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev
```

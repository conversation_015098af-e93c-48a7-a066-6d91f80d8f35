/**
 * 认证服务
 *
 * 提供登录、登出和token管理功能
 */

import {
  clearAccessToken,
  setAccessToken,
  userApiClient,
  userApiClientNoAuth,
} from "@/lib/api/management-api-client";
import { useMutation } from "@tanstack/react-query";

// 使用统一的API客户端
const getApiClientWithoutAuth = userApiClientNoAuth;
const getApiClient = userApiClient;

// 登录响应接口
export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: {
    id: string;
    name: string;
    role: string;
  };
}

// 登录请求接口
export interface LoginRequest {
  username: string;
  password: string;
}

// 修改密码接口
export interface PasswordModifyRequest {
  new_password: string;
}

/**
 * 管理员登录
 */
export const useLogin = () => {
  return useMutation({
    mutationFn: async (credentials: LoginRequest) => {
      const apiClient = await getApiClientWithoutAuth();
      return apiClient.post<LoginResponse>("/signin", credentials);
    },
    onSuccess(data) {
      setAccessToken(data.access_token);
    },
  });
};

/**
 * 管理员登出
 */
export const useLogout = () => {
  // const clearAuthState = useSetAtom(clearAuthStateAtom)
  return useMutation({
    mutationFn: async () => {
      // 调用登出API
      try {
        const apiClient = await getApiClient();
        await apiClient.post("/signout");
      } catch (error) {
        console.error("登出API调用失败", error);
      }
      // clearAuthState()
      // 无论API是否成功，都清除本地token
      clearAccessToken();

      return true;
    },
  });
};

/**
 * 检查用户是否已登录
 */
export const isAuthenticated = (): boolean => {
  if (typeof window === "undefined") return false;
  return !!localStorage.getItem("access_token");
};

/**
 * 修改密码
 */
export const usePasswordModify = () => {
  return useMutation({
    mutationFn: async (password: PasswordModifyRequest) => {
      const apiClient = await getApiClient();
      const response = await apiClient.post("/change-password", password);
      return response;
    },
  });
};

/**
 * 团队服务
 *
 * 提供团队相关的API请求方法
 */

import {
  createManagementApiClient,
  teamApiClient,
  type PaginatedResponse,
} from "@/lib/api/management-api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// 使用统一的API客户端
const getApiClient = teamApiClient;

// 分页专用的API客户端获取函数
const getPageApiClient = () =>
  createManagementApiClient("/team", {
    isPaginated: true,
  });

/**
 * 团队创建/更新参数
 */
export interface TeamParams {
  team_id?: string;
  admin_user_ids: string[]; // 只保留多选管理员
  name: string;
}

/**
 * 用户角色
 */
export interface UserRole {
  id: string;
  name: string;
}

/**
 * 管理员信息
 */
export interface AdminUser {
  id: string;
  login_name: string;
  nick: string;
  create_time: string;
  roles: UserRole[];
}

/**
 * 团队接口
 */
export interface TeamResponse {
  id: string;
  name: string;
  admins: AdminUser[];
  create_time: string;
  member_count: number;
}

/**
 * 团队成员接口
 */
export interface TeamMember {
  id: string;
  user: UserList;
  create_time: string;
  roles: string[];
}

export interface UserList {
  id: string;
  login_name: string;
}

/**
 * 获取团队列表（分页）
 *
 * @param pageNumber - 当前页码
 * @param pageSize - 每页条数
 */
export const useTeams = (pageNumber: number = 1, pageSize: number = 10, keyword?: string) => {
  return useQuery({
    queryKey: ["teams", pageNumber, pageSize, keyword],
    queryFn: async () => {
      const pageApiClient = await getPageApiClient();
      return pageApiClient.post<PaginatedResponse<TeamResponse>>(
        "/query",
        {
          keyword,
        },
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      );
    },
  });
};

/**
 * 分页获取团队成员信息
 */

export const useTeamsMember = (pageNumber: number = 1, pageSize: number = 10, team_id: string) => {
  return useQuery({
    queryKey: ["teams", pageNumber, pageSize, team_id],
    queryFn: async () => {
      const pageApiClient = await getPageApiClient();
      return pageApiClient.post<PaginatedResponse<TeamMember>>(
        "/query-members",
        {
          team_id,
        },
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      );
    },
  });
};

/**
 * 获取单个团队详情
 */
export const useTeam = () => {
  return useMutation({
    mutationFn: async (data: { team_id: string }) => {
      const apiClient = await getApiClient();
      return apiClient.post<TeamResponse>("/describe", data);
    },
  });
};

/**
 * 创建团队
 */
export const useCreateTeam = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (teamData: TeamParams) => {
      const apiClient = await getApiClient();
      return apiClient.post<TeamResponse>("/create", teamData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["teams"] });
    },
  });
};

/**
 * 更新团队
 */
export const useUpdateTeam = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (teamData: TeamParams) => {
      const apiClient = await getApiClient();
      return apiClient.post<TeamResponse>("/update", teamData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["teams"] });
    },
  });
};

/**
 * 删除团队
 */
export const useDeleteTeam = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { team_id: string }) => {
      const apiClient = await getApiClient();
      return apiClient.post("/delete", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["teams"] });
    },
  });
};

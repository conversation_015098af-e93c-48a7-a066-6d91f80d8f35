/**
 * 站点配置服务
 *
 * 提供站点配置相关的API请求方法
 */

import { createManagementApiClient } from "@/lib/api/management-api-client";
import { siteConfigAtom } from "@/store/user-store";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSetAtom } from "jotai";

// 使用统一的API客户端（无模块前缀，直接使用根路径）
const getApiClient = () => createManagementApiClient("");

export interface InitSiteConfig {
  dark_logo: string;
  favicon: string;
  login_name?: string;
  logo: string;
  mini_dark_logo: string;
  mini_logo: string;
  password?: string;
  site_name: string;
}

export interface TenantSiteConfig {
  favicon?: string;
  id?: string;
  logo?: string;
  mini_logo?: string;
  name?: string;
  tenant_id?: string;
  [property: string]: any;
}

export interface SiteConfigRequest {
  favicon?: string;
  logo?: string;
  mini_logo?: string;
  name?: string;
  [property: string]: any;
}

/**
 * 获取站点配置
 */
export const useSiteConfig = () => {
  const setSiteConfig = useSetAtom(siteConfigAtom);
  return useQuery({
    queryKey: ["siteConfig"],
    queryFn: async () => {
      const apiClient = await getApiClient();
      return apiClient.post<TenantSiteConfig>("/tenant/describe-site-config");
    },
    select: (data) => {
      setSiteConfig(data);
      return data;
    },
  });
};

/**
 * 网站初始化
 */
export const useInitSite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SiteConfigRequest) => {
      const apiClient = await getApiClient();
      return apiClient.post<InitSiteConfig>("/site/init", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["siteConfig"] });
    },
  });
};

/**
 * 更新站点配置
 */
export const useUpdateSiteConfig = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SiteConfigRequest) => {
      const apiClient = await getApiClient();
      return apiClient.post<void>("/tenant/modify-site-config", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["siteConfig"] });
    },
  });
};

/**
 * 上传文件
 * 使用 --data-binary 形式上传文件
 */
export const useUploadFile = () => {
  return useMutation({
    mutationFn: async (file: File) => {
      const apiClient = await getApiClient();
      return apiClient.post<string>(
        `/file/stream-upload?file_name=${encodeURIComponent(file.name)}`,
        file,
        {
          headers: {
            "Content-Type": "application/octet-stream",
          },
        }
      );
    },
  });
};

/**
 * 非鉴权上传文件
 * 使用 --data-binary 形式上传文件
 */
export const useSiteUploadFile = () => {
  return useMutation({
    mutationFn: async (file: File) => {
      const apiClient = await getApiClient();
      return apiClient.post<string>(
        `/site/init-stream-upload?file_name=${encodeURIComponent(file.name)}`,
        file,
        {
          headers: {
            "Content-Type": "application/octet-stream",
          },
        }
      );
    },
  });
};

import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin("./i18n.ts");
/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    MANAGEMENT_INNER_API_URL: process.env.MANAGEMENT_INNER_API_URL,
    MANAGEMENT_API_URL: process.env.MANAGEMENT_API_URL,
    API_PREFIX: process.env.API_PREFIX,
  },
  transpilePackages: ["@ragtop-web/ui"],

  output: "standalone",
  experimental: {
    optimizePackageImports: ["@ragtop-web/ui"],
    // nodeMiddleware: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
};

export default withNextIntl(nextConfig);

"use client";

import { useModelTypes } from "@/service/model-service";
import { zodResolver } from "@hookform/resolvers/zod";
import { Badge } from "@ragtop-web/ui/components/badge";
import { But<PERSON> } from "@ragtop-web/ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@ragtop-web/ui/components/command";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import { Popover, PopoverContent, PopoverTrigger } from "@ragtop-web/ui/components/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ragtop-web/ui/components/select";
import { cn } from "@ragtop-web/ui/lib/utils";
import { Check, ChevronsUpDown, Loader2, X } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { type User } from "../page";

// 防抖函数
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

const createFormSchema = (isCreating: boolean) =>
  z.object({
    name: z.string().min(1, "团队名称不能为空"),
    admin_user_ids: z.array(z.string()).min(1, "请至少选择一个团队管理员"),
    chat_model: isCreating ? z.string().min(1, "请选择适当的模型") : z.string().optional(),
    embd_model: isCreating ? z.string().min(1, "请选择适当的模型") : z.string().optional(),
    rerank_model: isCreating ? z.string().min(1, "请选择适当的模型") : z.string().optional(),
    nl2sql_model: isCreating ? z.string().min(1, "请选择适当的模型") : z.string().optional(),
    nl2python_model: isCreating ? z.string().min(1, "请选择适当的模型") : z.string().optional(),
  });

type FormValues = z.infer<ReturnType<typeof createFormSchema>>;

// 团队数据（用于表单）
interface TeamData {
  id?: string;
  name: string;
  admin_user_ids?: string[]; // 只保留多选管理员
  admins?: Array<{
    id: string;
    login_name: string;
    nick?: string;
  }>; // 完整的管理员信息
}

interface TeamFormProps {
  teamData?: TeamData;
  onSave: (formData: { name: string; admin_user_ids: string[] }) => void;
  isCreating: boolean;
  viewOnly?: boolean;
  queryUsers: (params: {
    page_number?: number;
    page_size?: number;
    keyword?: string;
  }) => Promise<any>;
}

export function TeamForm({
  teamData,
  onSave,
  isCreating,
  viewOnly = false,
  queryUsers,
}: TeamFormProps) {
  // 用户搜索状态
  const [open, setOpen] = useState(false);
  const [userKeyword, setUserKeyword] = useState("");
  const [users, setUsers] = useState<User[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [userPage, setUserPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [renderKey, setRenderKey] = useState(0); // 强制重新渲染的key
  const { chatOption, embdOption, rerankOption, nl2sqlOption, nl2codeOption } = useModelTypes();

  const formSchema = createFormSchema(isCreating);
  // 滚动加载引用
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // 请求状态引用 - 用于防止重复请求
  const requestRef = useRef<{
    isRequesting: boolean;
    lastKeyword: string;
    lastPage: number;
  }>({
    isRequesting: false,
    lastKeyword: "",
    lastPage: 0,
  });

  // 使用防抖处理搜索关键词
  const debouncedKeyword = useDebounce(userKeyword, 300);

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: teamData
      ? {
          name: teamData.name,
          admin_user_ids: teamData.admin_user_ids || [],
        }
      : {
          name: "",
          admin_user_ids: [],
        },
  });

  // 加载用户列表
  const loadUsers = useCallback(
    async (page = 1, keyword = "", append = false) => {
      // 如果已经在请求中，直接返回
      if (requestRef.current.isRequesting) {
        return;
      }

      // 如果是相同的请求参数且不是强制刷新，直接返回
      if (
        !append &&
        page === 1 &&
        page === requestRef.current.lastPage &&
        keyword === requestRef.current.lastKeyword
      ) {
        return;
      }

      // 标记请求状态
      requestRef.current.isRequesting = true;

      if (page === 1) {
        setHasMore(true);
      }

      setIsLoadingUsers(true);
      try {
        const response = await queryUsers({
          page_number: page,
          page_size: 10,
          keyword,
        });

        // 更新请求参数记录
        requestRef.current.lastPage = page;
        requestRef.current.lastKeyword = keyword;

        const newUsers = response.records || [];
        if (append) {
          setUsers((prev) => [...prev, ...newUsers]);
        } else {
          setUsers(newUsers);
        }

        // 强制重新渲染
        setRenderKey((prev) => prev + 1);

        setUserPage(page);

        // 检查是否还有更多数据
        setHasMore(newUsers.length > 0 && page * 10 < (response.total || 0));
      } catch (error) {
        console.error("加载用户失败", error);
      } finally {
        setIsLoadingUsers(false);
        requestRef.current.isRequesting = false;
      }
    },
    [queryUsers]
  );

  // 加载更多用户
  const loadMoreUsers = useCallback(() => {
    if (!isLoadingUsers && hasMore) {
      loadUsers(userPage + 1, debouncedKeyword, true);
    }
  }, [loadUsers, isLoadingUsers, hasMore, userPage, debouncedKeyword]);

  // 设置滚动观察器
  useEffect(() => {
    if (loadMoreRef.current) {
      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (entries.length > 0 && entries[0]?.isIntersecting && hasMore && !isLoadingUsers) {
            loadMoreUsers();
          }
        },
        { threshold: 0.1, rootMargin: "50px" }
      );

      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, isLoadingUsers]);

  // 监听滚动事件 - 优化滚动检测
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      // 避免重复触发
      if (isLoadingUsers || !hasMore) return;

      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      // 当滚动到距离底部 100px 时加载更多
      if (scrollHeight - scrollTop - clientHeight < 100) {
        loadMoreUsers();
      }
    },
    [loadMoreUsers, isLoadingUsers, hasMore]
  );

  // 监听下拉框打开状态和防抖后的关键词变化
  useEffect(() => {
    if (open) {
      // 当下拉框打开或关键词变化时，执行搜索
      loadUsers(1, debouncedKeyword, false);
    }
  }, [open, debouncedKeyword, loadUsers]);

  // 保存团队
  const handleSubmit = (values: FormValues) => {
    setIsSubmitting(true);

    // 调用保存函数
    try {
      onSave(values);
    } catch (error) {
      console.error("保存团队失败", error);
    } finally {
      // 延迟重置提交状态，以便显示加载效果
      setTimeout(() => {
        setIsSubmitting(false);
      }, 500);
    }
  };

  // 添加辅助函数：获取已选择的用户
  const getSelectedUsers = (selectedIds: string[]) => {
    const foundUsers = users.filter((user) => selectedIds.includes(user.id));

    // 如果在编辑模式下，需要处理可能还没有加载到的管理员
    if (teamData && foundUsers.length < selectedIds.length) {
      const missingIds = selectedIds.filter((id) => !foundUsers.some((user) => user.id === id));

      const missingUsers = missingIds.map((id) => {
        // 优先从传入的管理员信息中查找
        const adminInfo = teamData.admins?.find((admin) => admin.id === id);
        if (adminInfo) {
          return {
            id: adminInfo.id,
            login_name: adminInfo.login_name,
            nick: adminInfo.nick,
          };
        }

        // 如果没有找到，创建一个临时用户对象
        return {
          id: id,
          login_name: `用户-${id}`,
          nick: `用户-${id}`,
        };
      });

      return [...foundUsers, ...(missingUsers as User[])];
    }

    return foundUsers;
  };

  // 添加辅助函数：移除选中的用户
  const removeSelectedUser = (userId: string, field: any) => {
    const currentIds = field.value || [];
    const newIds = currentIds.filter((id: string) => id !== userId);
    field.onChange(newIds);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          rules={{ required: "请输入团队名称" }}
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>团队名称</RequiredFormLabel>
              <FormControl>
                <Input placeholder="输入团队名称" {...field} disabled={viewOnly} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="admin_user_ids"
          rules={{ required: "请至少选择一个团队管理员" }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <RequiredFormLabel>团队管理员</RequiredFormLabel>

              {/* 显示已选择的管理员 */}
              {field.value && field.value.length > 0 && (
                <div className="mb-2 flex flex-wrap gap-2">
                  {getSelectedUsers(field.value).map((user) => (
                    <Badge
                      key={user.id}
                      variant="secondary"
                      className="flex items-center gap-1 px-2 py-1"
                    >
                      <span>{user.login_name || user.nick || user.id}</span>
                      {!viewOnly && (
                        <button
                          type="button"
                          onClick={() => removeSelectedUser(user.id, field)}
                          className="hover:bg-destructive/10 ml-1 rounded-full p-0.5"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      )}
                    </Badge>
                  ))}
                </div>
              )}

              <Popover open={open} onOpenChange={setOpen} modal={true}>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      role="combobox"
                      disabled={viewOnly}
                      className={cn(
                        "w-full justify-between",
                        (!field.value || field.value.length === 0) && "text-muted-foreground"
                      )}
                    >
                      {field.value && field.value.length > 0
                        ? `已选择 ${field.value.length} 个管理员`
                        : "选择团队管理员"}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent
                  className="w-full p-0"
                  style={{ width: "var(--radix-popover-trigger-width)" }}
                >
                  <Command className="w-full" shouldFilter={false}>
                    <CommandInput
                      placeholder="搜索用户..."
                      value={userKeyword}
                      onValueChange={setUserKeyword}
                    />
                    {isLoadingUsers && userKeyword.length > 0 && (
                      <Loader2 className="h-4 w-4 animate-spin opacity-70" />
                    )}
                    <CommandList onScroll={handleScroll}>
                      {users.length === 0 && (
                        <CommandEmpty>
                          {isLoadingUsers && userKeyword.length === 0 ? (
                            <div className="flex items-center justify-center py-6">
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              <span>加载中...</span>
                            </div>
                          ) : isLoadingUsers && userKeyword.length > 0 ? (
                            <div className="flex items-center justify-center py-6">
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              <span>搜索中...</span>
                            </div>
                          ) : (
                            <div className="py-6 text-center">没有找到用户</div>
                          )}
                        </CommandEmpty>
                      )}
                      <CommandGroup key={renderKey}>
                        {users.map((user) => {
                          const isSelected = field.value?.includes(user.id) || false;
                          return (
                            <CommandItem
                              key={user.id}
                              value={user.login_name || user.nick || user.id}
                              onSelect={() => {
                                const currentIds = field.value || [];
                                if (isSelected) {
                                  // 取消选择
                                  const newIds = currentIds.filter((id: string) => id !== user.id);
                                  field.onChange(newIds);
                                } else {
                                  // 添加选择
                                  field.onChange([...currentIds, user.id]);
                                }
                              }}
                              className="flex items-center justify-between"
                            >
                              <div className="flex items-center">
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    isSelected ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                <span>{user.login_name || user.nick || user.id}</span>
                              </div>
                            </CommandItem>
                          );
                        })}

                        {/* 加载更多指示器 */}
                        {users.length > 0 && (
                          <div
                            ref={loadMoreRef}
                            className="text-muted-foreground py-2 text-center text-sm"
                          >
                            {isLoadingUsers ? (
                              <div className="flex items-center justify-center py-2">
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                <span>加载更多...</span>
                              </div>
                            ) : hasMore ? (
                              <span className="text-xs opacity-70">向下滚动加载更多</span>
                            ) : (
                              <span className="text-xs opacity-70">已加载全部用户</span>
                            )}
                          </div>
                        )}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormDescription>团队管理员拥有管理团队成员的权限</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {isCreating && (
          <>
            <FormField
              control={form.control}
              name="chat_model"
              rules={{ required: "请选择适当的聊天模型" }}
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>聊天模型</RequiredFormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-[415px]">
                        <SelectValue placeholder="请选择适当的聊天模型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {chatOption?.map((option) => (
                        <SelectItem key={option.llm_id} value={option.llm_id}>
                          <div className="flex items-center gap-2">
                            <span>{option.llm_alias || option.llm_name}</span>
                            {option.llm_alias && option.llm_name && (
                              <Badge variant="secondary" className="text-xs">
                                {option.llm_name}
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="embd_model"
              rules={{ required: "请选择适当的嵌入模型" }}
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>嵌入模型</RequiredFormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-[415px]">
                        <SelectValue placeholder="请选择适当的嵌入模型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="max-w-[var(--radix-select-trigger-width)]">
                      {embdOption?.map((option) => (
                        <SelectItem key={option.llm_id} value={option.llm_id}>
                          <div className="flex items-center gap-2">
                            <span>{option.llm_alias || option.llm_name}</span>
                            {option.llm_alias && option.llm_name && (
                              <Badge variant="secondary" className="text-xs">
                                {option.llm_name}
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="rerank_model"
              rules={{ required: "请选择适当的Rerank模型" }}
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>Rerank模型</RequiredFormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-[415px]">
                        <SelectValue placeholder="请选择适当的Rerank模型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {rerankOption?.map((option) => (
                        <SelectItem key={option.llm_id} value={option.llm_id}>
                          <div className="flex items-center gap-2">
                            <span>{option.llm_alias || option.llm_name}</span>
                            {option.llm_alias && option.llm_name && (
                              <Badge variant="secondary" className="text-xs">
                                {option.llm_name}
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="nl2sql_model"
              rules={{ required: "请选择适当的NL2SQL模型" }}
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>NL2SQL模型</RequiredFormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-[415px]">
                        <SelectValue placeholder="请选择适当的NL2SQL模型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {nl2sqlOption?.map((option) => (
                        <SelectItem key={option.llm_id} value={option.llm_id}>
                          <div className="flex items-center gap-2">
                            <span>{option.llm_alias || option.llm_name}</span>
                            {option.llm_alias && option.llm_name && (
                              <Badge variant="secondary" className="text-xs">
                                {option.llm_name}
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="nl2python_model"
              rules={{ required: "请选择适当的NL2PYTHON模型" }}
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>NL2PYTHON模型</RequiredFormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-[415px]">
                        <SelectValue placeholder="请选择适当的NL2PYTHON模型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {nl2codeOption?.map((option) => (
                        <SelectItem key={option.llm_id} value={option.llm_id}>
                          <div className="flex items-center gap-2">
                            <span>{option.llm_alias || option.llm_name}</span>
                            {option.llm_alias && option.llm_name && (
                              <Badge variant="secondary" className="text-xs">
                                {option.llm_name}
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}

        {!viewOnly && (
          <div className="flex justify-end gap-2">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && (
                <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              )}
              {isCreating ? "创建团队" : "保存修改"}
            </Button>
          </div>
        )}
      </form>
    </Form>
  );
}

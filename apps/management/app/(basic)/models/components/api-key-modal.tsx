"use client";

import { LLMFactory } from "@/common/model";
import { Model, useConfigurabledModels } from "@/service";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@ragtop-web/ui/components/button";
import { Checkbox } from "@ragtop-web/ui/components/checkbox";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@ragtop-web/ui/components/command";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import { Popover, PopoverContent, PopoverTrigger } from "@ragtop-web/ui/components/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ragtop-web/ui/components/select";
import { ChevronDown } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { IApiKeySavingParams } from "../hooks";

// 创建动态表单验证模式的函数
const maxTokensOptions = [
  { label: "32k", value: 32 * 1024 },
  { label: "64k", value: 64 * 1024 },
  { label: "128k", value: 128 * 1024 },
  { label: "256k", value: 256 * 1024 },
  { label: "512k", value: 512 * 1024 },
];

const allowedMaxTokens = maxTokensOptions.map((opt) => opt.value);

const createFormSchema = (
  requiresApiVersion: boolean,
  requiresBaseUrl: boolean,
  requiresApiKey: boolean
) =>
  z.object({
    api_key: requiresApiKey ? z.string().min(1, "API Key 不能为空") : z.string().optional(),
    base_url: requiresBaseUrl ? z.string().min(1, "基础URL不能为空") : z.string().optional(),
    model_purpose: z.string().min(1, "模型用途不能为空"),
    llm_name: z.string().min(1, "模型名称不能为空"),
    api_version: requiresApiVersion ? z.string().min(1, "API版本不能为空") : z.string().optional(),
    max_tokens: z.number().refine((val) => allowedMaxTokens.includes(val), {
      message: "请选择有效的最大Token数",
    }),
    llm_alias: z.string().min(1, "模型别名不能为空"),
    enable_thinking: z.boolean().optional(),
    model_type: z.string().min(1, "模型类型不能为空"),
  });

type FormValues = z.infer<ReturnType<typeof createFormSchema>>;

interface ApiKeyModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: IApiKeySavingParams) => void;
  loading?: boolean;
  initialApiKey?: string;
  llmFactory: Model;
  isEdit?: boolean;
}

const modelsWithVersion = [LLMFactory.AzureOpenAI];

const modelsWithBaseUrl = [
  LLMFactory.AzureOpenAI,
  LLMFactory.Ollama,
  LLMFactory.Xinference,
  LLMFactory.VLLM,
];

const modelWithApiKey = [LLMFactory.OpenAI, LLMFactory.TongYiQianWen, LLMFactory.AzureOpenAI];

const inputModelName = [
  LLMFactory.Ollama,
  LLMFactory.Xinference,
  LLMFactory.VLLM,
  LLMFactory.LocalAI,
];

export function ApiKeyModal({
  open,
  onClose,
  onSubmit,
  loading = false,
  initialApiKey = "",
  llmFactory,
  isEdit = false,
}: ApiKeyModalProps) {
  const {
    id,
    name,
    model_purposes,
    model_types = [],
    thinking_spec = {},
    ...restParams
  } = llmFactory;
  const { data: options } = useConfigurabledModels(id);
  const [comboboxOpen, setComboboxOpen] = useState(false);

  // 使用 useMemo 优化 tagOptions，避免每次渲染都重新创建
  const tagOptions = useMemo(() => {
    if (model_purposes) {
      return model_purposes.map((item) => ({
        value: item,
        label: item,
      }));
    }
    return [];
  }, [model_purposes]);

  // 检查是否需要API版本字段
  const requiresApiVersion = modelsWithVersion.some((x) => x === name);
  const requiresBaseUrl = modelsWithBaseUrl.some((x) => x === name);
  const requiresApiKey = modelWithApiKey.some((x) => x === name);

  // 创建对应的表单验证模式
  const formSchema = createFormSchema(requiresApiVersion, requiresBaseUrl, requiresApiKey);

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      api_key: initialApiKey,
      base_url: "",
      llm_name: "",
      model_purpose: tagOptions[0]?.value || "",
      api_version: "",
      model_type: "",
      max_tokens: 128 * 1024,
      enable_thinking: thinking_spec?.default_value ?? false,
    },
  });

  useEffect(() => {
    if (open && isEdit) {
      const initialValues = {
        api_key: "********",
        ...restParams,
        max_tokens: Number(restParams.max_tokens),
      };
      form.reset(initialValues);
    }
  }, [open, initialApiKey, isEdit]);

  // 监听模型用途的变化
  const selectedModelPurpose = form.watch("model_purpose");

  // 根据选择的模型用途过滤模型名称选项
  const filteredModelOptions = useMemo(() => {
    if (!options || !selectedModelPurpose) return [];

    return options.filter((option) => option.model_purpose?.toString() === selectedModelPurpose);
  }, [options, selectedModelPurpose]);

  // 当模型用途改变时，清空模型名称的选择
  useEffect(() => {
    if (selectedModelPurpose && !isEdit) {
      form.setValue("llm_name", "");
    }
  }, [selectedModelPurpose, form, isEdit]);

  // 处理表单提交
  const handleSubmit = (values: FormValues) => {
    // 处理api_key的逻辑
    let processedApiKey: string | null;

    if (values.api_key === "********") {
      processedApiKey = null;
    } else if (values.api_key === "" || !values.api_key) {
      // 如果是空，传空字符串
      processedApiKey = "";
    } else {
      // 其他情况返回实际字符串
      processedApiKey = values.api_key;
    }

    const finalValue: IApiKeySavingParams = {
      llm_factory_id: id,
      api_key: processedApiKey,
      base_url: values?.base_url || undefined,
      api_version: values?.api_version || undefined,
      llm_name: values.llm_name,
      model_purpose: values.model_purpose,
      max_tokens: values.max_tokens,
      llm_alias: values.llm_alias,
      enable_thinking: values.enable_thinking,
      model_type: values.model_type,
    };

    if (isEdit) {
      finalValue.id = restParams.id;
      finalValue.llm_id = restParams.llm_id;
    }
    onSubmit(finalValue);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-h-[95vh] overflow-y-auto sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>{isEdit ? "修改配置" : "添加LLM"}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="llm_alias"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>模型别名</RequiredFormLabel>
                  <FormControl>
                    <FormControl>
                      <Input placeholder={"请输入模型别名"} {...field} />
                    </FormControl>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="model_type"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>模型类型</RequiredFormLabel>
                  {isEdit ? (
                    <Input disabled={isEdit} placeholder="请输入模型类型" {...field} />
                  ) : (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="选择模型类型" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {model_types.map((option) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="model_purpose"
              rules={{ required: "请选择模型用途" }}
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>模型用途</RequiredFormLabel>
                  {isEdit ? (
                    <Input disabled={isEdit} placeholder="请输入模型用途" {...field} />
                  ) : (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full" disabled={isEdit}>
                          <SelectValue placeholder="选择模型用途" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {tagOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="llm_name"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>模型名称</RequiredFormLabel>
                  {inputModelName.includes(name as LLMFactory) ? (
                    <Input disabled={isEdit} placeholder="请输入模型名称" {...field} />
                  ) : (
                    <Popover open={comboboxOpen} onOpenChange={setComboboxOpen}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            disabled={isEdit}
                            aria-expanded={comboboxOpen}
                            className="w-full justify-between"
                          >
                            {field.value
                              ? filteredModelOptions.find(
                                  (option) => option.llm_name === field.value
                                )?.llm_name || field.value
                              : "选择或输入模型名称"}
                            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-[var(--radix-popover-trigger-width)] p-0"
                        align="start"
                      >
                        <Command>
                          <CommandInput
                            disabled={isEdit}
                            placeholder="搜索或输入模型名称..."
                            value={field.value}
                            onValueChange={(value) => {
                              field.onChange(value);
                            }}
                          />
                          <CommandList>
                            <CommandEmpty>
                              <div className="p-2">
                                <p className="text-muted-foreground text-sm">未找到匹配的模型</p>
                                {field.value && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="mt-2 w-full"
                                    onClick={() => {
                                      setComboboxOpen(false);
                                    }}
                                  >
                                    {`使用 "${field.value}"`}
                                  </Button>
                                )}
                              </div>
                            </CommandEmpty>
                            <CommandGroup>
                              {filteredModelOptions.map((option) => (
                                <CommandItem
                                  key={option.llm_name}
                                  value={option.llm_name}
                                  onSelect={(currentValue: string) => {
                                    field.onChange(currentValue);
                                    setComboboxOpen(false);
                                  }}
                                >
                                  {option.llm_name}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="api_key"
              render={({ field }) => (
                <FormItem>
                  {requiresApiKey ? (
                    <RequiredFormLabel>API-Key</RequiredFormLabel>
                  ) : (
                    <FormLabel>API-Key</FormLabel>
                  )}
                  <FormControl>
                    <Input
                      placeholder={"请输入api key (如果是本地部署的模型，请忽略它)"}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="base_url"
              render={({ field }) => (
                <FormItem>
                  {requiresBaseUrl ? (
                    <RequiredFormLabel>基础url</RequiredFormLabel>
                  ) : (
                    <FormLabel>基础url</FormLabel>
                  )}
                  <FormControl>
                    <Input placeholder="https://api.openai.com/v1" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {requiresApiVersion && (
              <FormField
                control={form.control}
                name="api_version"
                render={({ field }) => (
                  <FormItem>
                    <RequiredFormLabel>API版本</RequiredFormLabel>
                    <FormControl>
                      <Input placeholder="https://api.openai.com/v1" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* max_tokens 下拉选择框 */}
            <FormField
              control={form.control}
              name="max_tokens"
              rules={{ required: "请选择最大Token数" }}
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>最大Token数</RequiredFormLabel>
                  <Select
                    onValueChange={(val) => field.onChange(Number(val))}
                    value={field.value?.toString()}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="选择最大Token数" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {maxTokensOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value.toString()}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {thinking_spec?.enable_setting && (
              <FormField
                control={form.control}
                name="enable_thinking"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-2">
                      <FormControl>
                        <Checkbox checked={!!field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel className="mb-0 cursor-pointer" htmlFor="enable_thinking">
                        启用思考模式
                      </FormLabel>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                取消
              </Button>
              <Button type="submit" disabled={loading}>
                确定
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

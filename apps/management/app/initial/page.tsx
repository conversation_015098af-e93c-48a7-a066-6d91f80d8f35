"use client";

import { SiteConfigForm } from "@/components/site-config/site-config-form";
import { Card, CardContent, CardHeader, CardTitle } from "@ragtop-web/ui/components/card";

/**
 * 登录页面组件
 */
export default function LoginPage() {
  return (
    <Card className="mx-auto my-10 w-full max-w-2xl">
      <CardHeader className="space-y-1">
        <CardTitle className="mx-auto text-xl font-bold">初始化配置</CardTitle>
      </CardHeader>
      <CardContent>
        <SiteConfigForm
          isInit
          onClose={() => {
            window.location.replace("/login");
          }}
        />
      </CardContent>
    </Card>
  );
}

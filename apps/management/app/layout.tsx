import { AuthCheck } from "@/components/auth-check";
import { Providers } from "@/components/providers";
import { SiteConfigProvider } from "@/components/site-config-provider";
import "@ragtop-web/ui/globals.css";
import { NextIntlClientProvider } from "next-intl";
import { getLocale, getMessages } from "next-intl/server";

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();
  const messages = await getMessages();

  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body className={`font-sans antialiased`}>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <Providers>
            <SiteConfigProvider>
              <AuthCheck>
                <div className="relative flex min-h-svh flex-col">
                  <main className="flex-1">{children}</main>
                </div>
              </AuthCheck>
            </SiteConfigProvider>
          </Providers>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}

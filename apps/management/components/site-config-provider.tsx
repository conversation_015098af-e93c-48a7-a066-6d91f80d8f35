"use client";

import { useSiteConfig } from "@/service/site-config-service";
import { siteConfigAtom } from "@/store/user-store";
import { useAtomValue } from "jotai";
import Head from "next/head";
import { useEffect } from "react";

export function SiteConfigProvider({ children }: { children: React.ReactNode }) {
  const siteConfig = useAtomValue(siteConfigAtom);
  useSiteConfig();
  const baseUrl = process.env.MANAGEMENT_API_URL || "";

  // 处理查询结果，更新 atom 状态（避免重复设置相同数据）

  useEffect(() => {
    // 只有当 siteConfig 存在时才执行更新逻辑
    if (!siteConfig) {
      return;
    }

    // Update favicon when site config changes
    if (siteConfig.favicon) {
      const favicon = document.querySelector("link[rel='icon']");
      if (favicon) {
        favicon.setAttribute("href", `${baseUrl}/_${siteConfig.favicon}`);
      } else {
        const link = document.createElement("link");
        link.rel = "icon";
        link.href = `${baseUrl}/_${siteConfig.favicon}`;
        document.head.appendChild(link);
      }
    }

    // Update document title when site config changes
    if (siteConfig.name) {
      document.title = `${siteConfig.name} - 从知识和数据中快速获取洞察`;
      document
        .querySelector("meta[name='description']")
        ?.setAttribute(
          "content",
          `${siteConfig.name}是一项以个人和企业数据集为中心的人工智能服务，旨在释放数据的全部潜力。`
        );
    }
  }, [siteConfig, baseUrl]);

  return (
    <>
      <Head>
        {siteConfig?.favicon && <link rel="icon" href={`${baseUrl}/_${siteConfig.favicon}`} />}
      </Head>
      {children}
    </>
  );
}

"use client";

import { PasswordChangeDialog } from "@/components/password-change-dialog";
import { SiteConfigDialog } from "@/components/site-config/site-config-dialog";
import { useLogout } from "@/service";
import { currentUserAtom } from "@/store/user-store";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@ragtop-web/ui/components/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@ragtop-web/ui/components/sidebar";
import { useAtomValue } from "jotai";
import { ChevronsUpDown, KeyRound, LogOut, Settings } from "lucide-react";
import { useState } from "react";

export function NavUser() {
  const { isMobile } = useSidebar();
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [isSiteConfigDialogOpen, setIsSiteConfigDialogOpen] = useState(false);
  const logoutMutation = useLogout();
  const currentUser = useAtomValue(currentUserAtom);

  // 处理打开密码修改对话框
  const handleOpenPasswordDialog = () => {
    setIsPasswordDialogOpen(true);
  };

  // 处理关闭密码修改对话框
  const handleClosePasswordDialog = () => {
    setIsPasswordDialogOpen(false);
  };

  // 处理打开站点配置对话框
  const handleOpenSiteConfigDialog = () => {
    setIsSiteConfigDialogOpen(true);
  };

  // 处理关闭站点配置对话框
  const handleCloseSiteConfigDialog = () => {
    setIsSiteConfigDialogOpen(false);
  };

  // 处理退出登录
  const handleLogout = () => {
    logoutMutation.mutate(undefined, {
      onSuccess: () => {
        // 跳转到登录页面
        window.location.href = "/login";
        // router.push("/login")
      },
    });
  };

  return (
    <>
      <SidebarMenu>
        <SidebarMenuItem>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                size="lg"
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">{currentUser?.login_name}</span>
                </div>
                <ChevronsUpDown className="ml-auto size-4" />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
              side={isMobile ? "bottom" : "right"}
              align="end"
              sideOffset={4}
            >
              <DropdownMenuLabel className="p-0 font-normal">
                <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-medium">{currentUser?.login_name}</span>
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem onClick={handleOpenPasswordDialog}>
                  <KeyRound className="mr-2 h-4 w-4" />
                  修改密码
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleOpenSiteConfigDialog}>
                  <Settings className="mr-2 h-4 w-4" />
                  站点配置
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="mr-2 h-4 w-4" />
                退出登录
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>

      {/* 密码修改对话框 */}
      <PasswordChangeDialog open={isPasswordDialogOpen} onClose={handleClosePasswordDialog} />

      {/* 站点配置对话框 */}
      {isSiteConfigDialogOpen && (
        <SiteConfigDialog open={isSiteConfigDialogOpen} onClose={handleCloseSiteConfigDialog} />
      )}
    </>
  );
}

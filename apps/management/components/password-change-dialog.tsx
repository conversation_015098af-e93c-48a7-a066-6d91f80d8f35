"use client";

import { usePasswordModify } from "@/service/auth-service";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@ragtop-web/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

// 表单验证模式
const formSchema = z
  .object({
    newPassword: z.string().min(6, "新密码至少需要6个字符"),
    confirmPassword: z.string().min(6, "确认密码至少需要6个字符"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "新密码与确认密码不匹配",
    path: ["confirmPassword"],
  });

type FormValues = z.infer<typeof formSchema>;

interface PasswordChangeDialogProps {
  open: boolean;
  onClose: () => void;
}

/**
 * 密码修改对话框组件
 */
export function PasswordChangeDialog({ open, onClose }: PasswordChangeDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const passwordModifyMutation = usePasswordModify();
  const { toast } = useToast();

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    },
  });

  const handleClose = () => {
    form.reset();
    onClose();
  };

  // 处理密码修改
  const handleSubmit = (values: FormValues) => {
    setIsLoading(true);
    setError("");

    passwordModifyMutation.mutate(
      { new_password: values.newPassword },
      {
        onSuccess: () => {
          toast({
            title: "密码修改成功",
          });
          form.reset();
          handleClose();
        },
        onError: () => {
          setError("修改密码失败");
          setIsLoading(false);
        },
      }
    );
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>修改超级管理员密码</DialogTitle>
          <DialogDescription>请输入当前密码和新密码以完成修改</DialogDescription>
        </DialogHeader>

        {error && (
          <div className="bg-destructive/15 text-destructive mb-2 rounded-md p-3 text-sm">
            {error}
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>新密码</RequiredFormLabel>
                  <FormControl>
                    <Input type="password" placeholder="输入新密码" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>确认新密码</RequiredFormLabel>
                  <FormControl>
                    <Input type="password" placeholder="再次输入新密码" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
                取消
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <span className="flex items-center gap-2">
                    <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    提交中...
                  </span>
                ) : (
                  "确认修改"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

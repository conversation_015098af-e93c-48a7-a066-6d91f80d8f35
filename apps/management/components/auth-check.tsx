"use client";

import { isAuthenticated } from "@/service/auth-service";
import { useCurrentUser } from "@/service/user-service";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface AuthCheckerProps {
  children: React.ReactNode;
}

export function AuthCheck({ children }: AuthCheckerProps) {
  const currentUser = useCurrentUser();
  const [isChecking, setIsChecking] = useState(true);
  const [isAuth, setIsAuth] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // 跳过登录页面和初始化页面的检查
    if (pathname === "/login" || pathname === "/initial") {
      setIsChecking(false);
      setIsAuth(true); // 允许访问这些页面
      return;
    }

    const checkAuth = () => {
      const isLoggedIn = isAuthenticated();

      if (!isLoggedIn) {
        // 没有token，跳转到登录页面
        router.push("/login");
        return;
      } else {
        currentUser.mutate(undefined);
      }

      // 有token，允许访问
      setIsAuth(true);
      setIsChecking(false);
    };

    checkAuth();
  }, [pathname, router]);

  // 正在检查状态时显示加载页面
  if (isChecking) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-current border-t-transparent"></div>
          <p className="text-muted-foreground text-sm">正在验证身份...</p>
        </div>
      </div>
    );
  }

  // 已认证或在允许的页面，显示内容
  if (isAuth) {
    return <>{children}</>;
  }

  // 未认证且不在允许的页面，不显示内容（会被重定向）
  return null;
}

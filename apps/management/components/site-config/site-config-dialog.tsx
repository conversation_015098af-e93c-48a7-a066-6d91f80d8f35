"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import { SiteConfigForm } from "./site-config-form";

interface SiteConfigDialogProps {
  open: boolean;
  onClose: () => void;
}

export function SiteConfigDialog({ open, onClose }: SiteConfigDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="flex flex-col">
        <DialogHeader className="flex-none">
          <DialogTitle>站点配置</DialogTitle>
          <DialogDescription>配置站点名称、Logo和图标</DialogDescription>
        </DialogHeader>
        <SiteConfigForm onClose={onClose} />
      </DialogContent>
    </Dialog>
  );
}

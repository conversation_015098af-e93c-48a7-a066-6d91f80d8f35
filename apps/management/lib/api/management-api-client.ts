/**
 * 管理应用专用的API客户端配置
 *
 * 基于UI库的fetch-client，为管理应用统一初始化baseUrl和apiPrefix
 */

import type { FetchOptions } from "@ragtop-web/ui/lib/api";
import { createApiClient } from "@ragtop-web/ui/lib/api";
import { connection } from "next/server";

/**
 * 创建管理应用专用的API客户端
 * 自动配置baseUrl和apiPrefix
 *
 * @param modulePrefix - 模块路径前缀，如 '/user', '/llm', '/team' 等
 * @param options - 额外的配置选项
 * @returns API客户端实例
 */
export const createManagementApiClient = async (
  modulePrefix: string = "",
  options: Partial<FetchOptions> = {}
) => {
  await connection();
  const MANAGEMENT_API_PREFIX = process.env.API_PREFIX || "/api/v1/portal-ragtop-admin";
  const MANAGEMENT_BASE_URL = process.env.MANAGEMENT_API_URL || "";

  const finalApiPrefix = `${MANAGEMENT_API_PREFIX}${modulePrefix}`;

  return createApiClient({
    baseUrl: MANAGEMENT_BASE_URL,
    apiPrefix: finalApiPrefix,
    ...options,
  });
};

/**
 * 创建不需要认证的管理API客户端
 * 主要用于登录等接口
 *
 * @param modulePrefix - 模块路径前缀
 * @param options - 额外的配置选项
 * @returns 无需认证的API客户端实例
 */
export const createManagementApiClientNoAuth = async (
  modulePrefix: string = "",
  options: Partial<FetchOptions> = {}
) => {
  await connection();
  const MANAGEMENT_API_PREFIX = process.env.API_PREFIX || "/api/v1/portal-ragtop-admin";
  const MANAGEMENT_BASE_URL = process.env.MANAGEMENT_API_URL || "";

  const finalApiPrefix = `${MANAGEMENT_API_PREFIX}${modulePrefix}`;

  return createManagementApiClient(modulePrefix, {
    baseUrl: MANAGEMENT_BASE_URL,
    apiPrefix: finalApiPrefix,
    requireAuth: false,
    ...options,
  });
};

/**
 * 预定义的常用模块API客户端
 */

/**
 * 用户模块API客户端（需要认证）
 */
export const userApiClient = () => createManagementApiClient("/user");

/**
 * 用户模块API客户端（无需认证）
 * 用于登录等不需要认证的接口
 */
export const userApiClientNoAuth = () => createManagementApiClientNoAuth("/user");

/**
 * 模型模块API客户端
 */
export const llmApiClient = () => createManagementApiClient("/llm");

/**
 * 团队模块API客户端
 */
export const teamApiClient = () => createManagementApiClient("/team");

/**
 * 站点配置模块API客户端
 */
export const siteConfigApiClient = () => createManagementApiClient("/site-config");

// 重新导出其他需要的函数和类型
export {
  API_BAD_CREDENTIAL_CODE,
  API_SUCCESS_CODE,
  ApiError,
  clearAccessToken,
  del,
  fetchClient,
  get,
  getAccessToken,
  patch,
  post,
  put,
  setAccessToken,
  type ApiResponse,
  type FetchOptions,
  type PaginatedResponse,
} from "@ragtop-web/ui/lib/api";

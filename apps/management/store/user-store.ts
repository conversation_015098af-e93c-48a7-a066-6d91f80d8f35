/**
 * 团队状态管理
 *
 * 使用Jotai管理团队相关的状态，结合localStorage持久化存储
 */

import { TenantSiteConfig } from "@/service/site-config-service";
import { User } from "@/service/user-service";
import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";

// 定义登录响应中的团队类型
export interface LoginTeam {
  id: string;
  title: string;
  roles?: string[];
}

/**
 * 当前用户信息的atom
 * 使用localStorage持久化存储
 */
export const currentUserAtom = atomWithStorage<User | null>("current_user", null, undefined, {
  getOnInit: true,
});

/**
 * 清理所有认证相关状态的写入atom
 * 用于退出登录或认证失效时
 */
export const clearAuthStateAtom = atom(null, (_, set) => {
  // 清理用户信息
  set(currentUserAtom, null);
});

export const siteConfigAtom = atom<TenantSiteConfig | null>(null);

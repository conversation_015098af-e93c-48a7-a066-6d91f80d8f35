/**
 * 数据集服务
 *
 * 提供数据集相关的API请求方法
 */

import { createWebApiClient, PaginatedResponse } from "@/lib/api/web-api-client";
import { useInfiniteQueryScroll } from "@ragtop-web/ui/hooks/use-infinite-scroll";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { API_PREFIX } from "./common";
import { ResType } from "./types";

// 创建API客户端
const getApiClient = () =>
  createWebApiClient({
    apiPrefix: `${API_PREFIX}/tableset`,
  });

// 文件数据集中文件相关操作API客户端
const getApiClientForTablesetDoc = () =>
  createWebApiClient({
    apiPrefix: `${API_PREFIX}/tableset-doc`,
  });

// 数据集接口
export interface Dataset {
  create_time?: Date;
  description?: string;
  id: string;
  infra_res_id?: string;
  name?: string;
  /**
   * 资源类型
   */
  res_type?: ResType;
  [property: string]: any;
}

/**
 * DbTablesetRes，response#data
 */
export interface DbTablesetRes {
  build_version?: string;
  building_version?: string;
  create_time?: Date;
  description?: string;
  id?: string;
  infra_res_id?: string;
  meta?: Meta;
  name?: string;
  /**
   * 资源类型
   */
  res_type?: ResType;
  [property: string]: any;
}

/**
 * Meta
 */
export interface Meta {
  database?: string;
  password?: string;
  username?: string;
  [property: string]: any;
}

/**
 *  数据集创建参数
 */
export interface CreateDatasetParams {
  database?: string;
  description?: string;
  name?: string;
  password?: string;
  team_id?: string;
  username?: string;
  [property: string]: any;
}

/**
 * ModifyDbTablesetRequest
 */
export interface UpdateDatasetParams {
  description?: string;
  name?: string;
  tableset_id?: string;
  team_id?: string;
  [property: string]: any;
}

export interface TableSchema {
  annotation?: string;
  comment?: string;
  table_name?: string;
  schemaId?: string;
  [property: string]: any;
}
export interface DbSchema {
  annotation?: string;
  comment?: string;
  schema_name?: string;
  [property: string]: any;
}

/**
 * 修改schema注释
 */
export interface DocumentAnnotation {
  column_name?: string;
  comment?: string;
  schema_name?: string;
  table_name?: string;
  tableset_id?: string;
  team_id?: string;
  [property: string]: any;
}

/**
 * ListDbTableColumnRequest
 */
export interface DocumentColumnsParams {
  schema_name?: string;
  table_name?: string;
  tableset_id?: string;
  team_id?: string;
  [property: string]: any;
}

export interface DbTableColumn {
  annotation?: string;
  column_name?: string;
  comment?: string;
  data_type?: string;
  [property: string]: any;
}

/**
 * TablesetReindexRequest
 */
export interface ReindexRequest {
  doc_id?: string;
  tableset_id?: string;
  team_id?: string;
  worksheets?: Sheet[];
  [property: string]: any;
}

/**
 * Sheet
 */
export interface Sheet {
  name?: string;
  semantic_name?: string;
  semantic_comment?: string;
  subtables?: SubtableInfo[];
  [property: string]: any;
}

/**
 * SubtableInfo
 */
export interface SubtableInfo {
  columns?: ColumnInfo[];
  data_range?: string;
  footnote?: string;
  footnote_range?: string;
  range?: string;
  title?: string;
  title_range?: string;
  [property: string]: any;
}

/**
 * ColumnInfo
 */
export interface ColumnInfo {
  data_type?: string;
  name?: string;
  range?: string;
  semantic_name?: string;
  semantic_comment?: string;
  [property: string]: any;
}

/**
 * 获取数据集列表
 * 分页查询
 */
export const useDatasets = (pageNumber = 1, pageSize = 10, enabled = true) => {
  return useQuery({
    queryKey: ["datasets", pageNumber, pageSize],
    queryFn: async () => {
      const apiClient = await getApiClient();
      return apiClient.post<PaginatedResponse<Dataset>>(
        "/db/query",
        {},
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      );
    },
    enabled,
    refetchInterval: ({ state }) => {
      const result = state?.data?.records;
      const hasRunning = result?.find(({ building_version }) => building_version);
      return hasRunning ? 10000 : false; // 返回 false 表示停止轮询
    },
    gcTime: 0,
  });
};

/**
 * 获取数据集列表 - 无限滚动版本
 * 使用 React Query 的 useInfiniteQuery
 */
export const useDatasetsInfinite = (pageSize = 10, enabled = true) => {
  return useInfiniteQueryScroll<Dataset>({
    queryKey: ["datasets", "infinite"],
    queryFn: async ({ pageNumber = 1, pageSize: size = pageSize }) => {
      const apiClient = await getApiClient();
      const response = await apiClient.post<PaginatedResponse<Dataset>>(
        "/db/query",
        {},
        {
          isPaginated: true,
          pageNumber,
          pageSize: size,
        }
      );
      return {
        records: response.records || [],
        total: response.total || 0,
      };
    },
    pageSize,
    enabled,
  });
};

/**
 * 获取单个数据集
 */
export const useDataset = (tableset_id: string, enabled = true) => {
  return useQuery({
    queryKey: ["dataset", tableset_id],
    queryFn: async () => {
      const apiClient = await getApiClient();
      return apiClient.post<Dataset>("/db/describe", { tableset_id });
    },
    enabled: !!tableset_id && enabled,
    refetchInterval: ({ state }) => {
      const result = state?.data?.building_version;
      return result ? 10000 : false; // 返回 false 表示停止轮询
    },
    gcTime: 0,
  });
};

/**
 * 创建数据集
 */
export const useCreateDataset = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateDatasetParams) => {
      const apiClient = await getApiClient();
      return apiClient.post(`/db/create`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["datasets"] });
    },
  });
};

/**
 * 更新数据集
 */
export const useUpdateDataset = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateDatasetParams) => {
      const apiClient = await getApiClient();
      return apiClient.post(`/db/modify`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["datasets"] });
    },
  });
};

/**
 * 删除数据集
 */
export const useDeleteDataset = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { tableset_id: string }) => {
      const apiClient = await getApiClient();
      return apiClient.post(`/delete`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["datasets"] });
    },
  });
};

/**
 * 构建数据集
 */
export const useBuildDataset = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { tableset_id: string }) => {
      const apiClient = await getApiClient();
      return apiClient.post(`/db/build`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["datasets"] });
      queryClient.invalidateQueries({ queryKey: ["dataset"] });
    },
  });
};

/**
 * 获取数据集表结构
 */
export const useDatasetSchemas = (id: string, pageNumber = 1, pageSize = 10, enabled = true) => {
  return useQuery({
    queryKey: ["datasetDocuments", id, pageNumber, pageSize],
    queryFn: async () => {
      const apiClient = await getApiClient();
      return apiClient.post<PaginatedResponse<DbSchema>>(
        "/db/describe_schemas",
        { tableset_id: id },
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      );
    },
    enabled: !!id && enabled,
    gcTime: 0,
  });
};

/**
 * 获取数据集表结构 - 无限滚动版本
 */
export const useDatasetSchemasInfinite = (id: string, pageSize = 20, enabled = true) => {
  return useInfiniteQueryScroll<DbSchema>({
    queryKey: ["datasetSchemas", "infinite", id],
    queryFn: async ({ pageNumber = 1, pageSize: size = pageSize }) => {
      const apiClient = await getApiClient();
      const response = await apiClient.post<PaginatedResponse<DbSchema>>(
        "/db/describe_schemas",
        { tableset_id: id },
        {
          isPaginated: true,
          pageNumber,
          pageSize: size,
        }
      );
      return {
        records: response.records || [],
        total: response.total || 0,
      };
    },
    pageSize,
    enabled: enabled && !!id,
    staleTime: 0,
    extraParams: { id },
  });
};

/**
 * 根据表结构查询table
 */

export const useDatasetTables = (pageNumber = 1, pageSize = 10) => {
  return useMutation({
    mutationFn: async (data: Omit<DocumentColumnsParams, "table_name">) => {
      const apiClient = await getApiClient();
      return apiClient.post<PaginatedResponse<TableSchema>>(`/db/describe_tables`, data, {
        isPaginated: true,
        pageNumber,
        pageSize,
      });
    },
  });
};

/**
 * 根据表结构查询table —— 无限滚动版本
 */
export const useDatasetTablesInfinite = (
  schema_name: string,
  tableset_id: string,
  pageSize = 10,
  enabled = true
) => {
  return useInfiniteQueryScroll<TableSchema>({
    queryKey: ["tables", "infinite", schema_name, tableset_id],
    queryFn: async ({ pageNumber = 1, pageSize: size = pageSize }) => {
      const apiClient = await getApiClient();
      const response = await apiClient.post<PaginatedResponse<TableSchema>>(
        "/db/describe_tables",
        { schema_name, tableset_id },
        {
          isPaginated: true,
          pageNumber,
          pageSize: size,
        }
      );
      return {
        records: response.records || [],
        total: response.total || 0,
      };
    },
    pageSize,
    enabled,
    extraParams: { schema_name, tableset_id },
  });
};

/**
 * 获取数据集表文档
 */
export const useDatasetDocuments = () => {
  return useMutation({
    mutationFn: async (data: DocumentColumnsParams) => {
      const apiClient = await getApiClient();
      return apiClient.post<DbTableColumn[]>(`/db/describe_table_columns`, data);
    },
  });
};

/**
 * 更新文档注释
 */
export const useUpdateDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: DocumentAnnotation) => {
      const apiClient = await getApiClient();
      return apiClient.post(`/db/modify_annotation`, data);
    },
    onSuccess: (data, variables) => {
      if (!variables?.column_name) {
        queryClient.invalidateQueries({ queryKey: ["tables", "infinite"] });
      }
      if (!variables?.table_name) {
        queryClient.invalidateQueries({
          queryKey: ["datasetSchemas", "infinite"],
        });
      }
    },
  });
};

/**
 * 测试数据集连接
 */
export const useTestConnection = () => {
  return useMutation({
    mutationFn: async (data: Omit<CreateDatasetParams, "description" | "name">) => {
      const apiClient = await getApiClient();
      return apiClient.post<{ success: boolean; message: string }>(`/db/test-connection`, data);
    },
  });
};

/**
 * 获取文件数据集列表
 */
export const useFilesets = (pageNumber = 1, pageSize = 10, enabled = true) => {
  return useQuery({
    queryKey: ["filesets", pageNumber, pageSize],
    queryFn: async () => {
      const apiClient = await getApiClient();
      return apiClient.post<PaginatedResponse<Dataset>>(
        "/file/query",
        {},
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      );
    },
    enabled,
  });
};

/**
 * 获取文件数据集列表 - 无限滚动版本
 * 使用 React Query 的 useInfiniteQuery
 */
export const useFilesetsInfinite = (pageSize = 10, enabled = true) => {
  return useInfiniteQueryScroll<Dataset>({
    queryKey: ["filesets", "infinite"],
    queryFn: async ({ pageNumber = 1, pageSize: size = pageSize }) => {
      const apiClient = await getApiClient();
      const response = await apiClient.post<PaginatedResponse<Dataset>>(
        "/file/query",
        {},
        {
          isPaginated: true,
          pageNumber,
          pageSize: size,
        }
      );
      return {
        records: response.records || [],
        total: response.total || 0,
      };
    },
    pageSize,
    enabled,
  });
};

/**
 * 创建新文件数据集
 */
export const useCreateFileset = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateDatasetParams) => {
      const apiClient = await getApiClient();
      return apiClient.post(`/file/create`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filesets"] });
    },
  });
};

/**
 * 删除文件数据集
 */
export const useDeleteFileset = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateDatasetParams) => {
      const apiClient = await getApiClient();
      return apiClient.post(`/delete`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filesets"] });
    },
  });
};

/**
 * 修改文件数据集
 */
export const useUpdateFileset = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateDatasetParams) => {
      const apiClient = await getApiClient();
      return apiClient.post(`/file/modify`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filesets"] });
    },
  });
};

/**
 * 获取单个文件数据集
 */
export const useFileset = (tableset_id: string, enabled = true) => {
  return useQuery({
    queryKey: ["fileset", tableset_id],
    queryFn: async () => {
      const apiClient = await getApiClient();
      return apiClient.post<Dataset>("/file/describe", { tableset_id });
    },
    enabled: !!tableset_id && enabled,
    gcTime: 0,
  });
};

/**
 * 向文件数据集中添加文档
 */
export const useAddFileSetDocuments = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { tableset_id: string; file_ids: string[] }) => {
      const apiClient = await getApiClient();
      return apiClient.post("/file/add-team-files", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filesetDocList"] });
    },
  });
};

// export const useFilesetDoc = (
//   id = "",
//   pageNumber = 1,
//   pageSize = 10,
//   enabled = true
// ) => {
//   return useQuery({
//     queryKey: ["filesetDoc", pageNumber, pageSize, id],
//     queryFn: () =>
//       apiClient.post<PaginatedResponse<KnowledgeBaseDocList>>(
//         "/query",
//         { kbase_id: id },
//         {
//           isPaginated: true,
//           pageNumber,
//           pageSize,
//         }
//       ),
//     refetchInterval: ({ state }) => {
//       const result = state?.data?.records;
//       const hasRunning = result?.find(
//         ({ run_status }) => run_status === RunStatus.Running
//       );
//       return hasRunning ? 15000 : false; // 返回 false 表示停止轮询
//     },
//     enabled: !!id && enabled,
//   });
// };

/**
 * 运行状态
 */
export enum RunStatus {
  Cancel = "CANCEL",
  Done = "DONE",
  Fail = "FAIL",
  Cancelling = "CANCELLING",
  Running = "RUNNING",
  Unstart = "UNSTART",
}

/**
 * 获取关联的文件列表
 * TODO: 分页查询
 */
export const useFilesetDocList = (id = "", pageNumber = 1, pageSize = 10, enabled = true) => {
  return useQuery({
    queryKey: ["filesetDocList", pageNumber, pageSize, id],
    queryFn: async () => {
      const apiClientForTablesetDoc = await getApiClientForTablesetDoc();
      return apiClientForTablesetDoc.post<PaginatedResponse<any>>(
        "/query",
        { tableset_id: id },
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      );
    },
    refetchInterval: ({ state }) => {
      const result = state?.data?.records;
      const hasRunning = result?.find(({ run_status }) =>
        [RunStatus.Running, RunStatus.Cancelling].includes(run_status)
      );
      return hasRunning ? 15000 : false; // 返回 false 表示停止轮询
    },
    enabled: !!id && enabled,
    gcTime: 0,
  });
};

/**
 * 重置解析
 */
export const useFilesetDocReload = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: { doc_id: string; tableset_id: string }) => {
      const apiClientForTablesetDoc = await getApiClientForTablesetDoc();
      return apiClientForTablesetDoc.post(`/reload`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filesetDocList"] });
    },
  });
};

/**
 * 保存并解析
 */
export const useFilesetDocSaveAndParse = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: ReindexRequest) => {
      const apiClientForTablesetDoc = await getApiClientForTablesetDoc();
      return apiClientForTablesetDoc.post(`/reindex`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filesetDocList"] });
    },
  });
};

/**
 * 请求元数据
 */
export const useFilesetDocRequestMetadata = ({
  tableset_id,
  doc_id,
}: {
  tableset_id: string;
  doc_id: string;
}) => {
  return useQuery({
    queryKey: ["filesetDocRequestMetadata", tableset_id, doc_id],
    queryFn: async () => {
      const apiClientForTablesetDoc = await getApiClientForTablesetDoc();
      return apiClientForTablesetDoc.post<Sheet[]>(`/inspect`, {
        tableset_id,
        doc_id,
      });
    },
    gcTime: 0,
  });
};
/**
 * 开始解析
 * @returns
 */

export const useFilesetDocStartParse = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: { document_ids: string[] }) => {
      const apiClientForTablesetDoc = await getApiClientForTablesetDoc();
      return apiClientForTablesetDoc.post(`/batch-start-parse`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filesetDocList"] });
    },
  });
};

/**
 * 终止解析
 * @returns
 */
export const useFilesetDocStopParse = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: { document_ids: string[] }) => {
      const apiClientForTablesetDoc = await getApiClientForTablesetDoc();
      return apiClientForTablesetDoc.post(`/batch-stop-parse`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filesetDocList"] });
    },
  });
};

/**
 * 将文档从文件数据集中移除
 * @returns
 */
export const useFilesetDocDelete = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { document_ids: string[] }) => {
      const apiClientForTablesetDoc = await getApiClientForTablesetDoc();
      return apiClientForTablesetDoc.post(`/batch-delete`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filesetDocList"] });
    },
  });
};

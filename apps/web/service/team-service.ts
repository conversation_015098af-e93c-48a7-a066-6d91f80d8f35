/**
 * 团队服务
 *
 * 提供团队相关的API请求方法和团队ID管理
 */

import { createWebApiClient } from "@/lib/api/web-api-client";
import { switchTeamAtom } from "@/store/team-store";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSetAtom } from "jotai";
import { API_PREFIX } from "./common";

// 成员API客户端
const getMemberApiClient = () =>
  createWebApiClient({
    apiPrefix: `${API_PREFIX}/member`,
  });

export enum TeamType {
  Personal = "PERSONAL",
  General = "GENERAL",
}

export interface Team {
  id: string;
  roles: string[];
  title: string;
  member_id: string;
  type: TeamType;
}

export interface User {
  create_time: number;
  id: string;
  nick: string;
  teams: Team[];
  username: string;
}

// 团队成员详情接口
export interface UserList {
  create_time: number;
  id: string;
  roles?: string[];
  user: User;
}

/**
 * 切换当前团队
 * 切换后会刷新页面重新获取数据
 */
export const useSwitchTeam = () => {
  const switchTeam = useSetAtom(switchTeamAtom);

  return useMutation({
    mutationFn: (team: Team) => {
      // 使用switchTeamAtom切换团队，会自动刷新页面
      switchTeam(team);
      return Promise.resolve(team);
    },
  });
};

/**
 * 获取用户列表（分页）
 *
 * @param pageNumber - 当前页码
 * @param pageSize - 每页条数
 * @param keyword - 搜索关键词
 */
export const useMembers = (pageNumber: number = 1, pageSize: number = 10, keyword?: string) => {
  return useQuery({
    queryKey: ["teams", pageNumber, pageSize, keyword],
    queryFn: async () => {
      const memberApiClient = await getMemberApiClient();
      return memberApiClient.post<{
        records: UserList[];
        total: number;
        page_number: number;
        page_size: number;
      }>(
        "/query",
        {
          keyword,
        },
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      );
    },
  });
};

/**
 * 添加成员
 */
export const useMemberCreate = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: any) => {
      const memberApiClient = await getMemberApiClient();
      return memberApiClient.post(`/create`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["teams"] });
    },
  });
};

/**
 * 删除成员
 */
export const useMemberDelete = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: any) => {
      const memberApiClient = await getMemberApiClient();
      return memberApiClient.post(`/delete`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["teams"] });
    },
  });
};

/**
 * 查询某个成员详细信息
 */
export const useMemberDescribe = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: any) => {
      const memberApiClient = await getMemberApiClient();
      return memberApiClient.post<User>(`current`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["teams"] });
    },
  });
};

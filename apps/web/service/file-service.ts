/**
 * 文件服务
 *
 * 提供文件相关的API请求方法
 */

import { createWebApiClient, PaginatedResponse } from "@/lib/api/web-api-client";
import { getTeamId } from "@/store/team-store";
import {
  useInfiniteQueryScroll,
  type InfiniteScrollParams,
} from "@ragtop-web/ui/hooks/use-infinite-scroll";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { API_PREFIX } from "./common";

// 创建API客户端
const getApiClient = () =>
  createWebApiClient({
    apiPrefix: `${API_PREFIX}/file`,
  });

const getApiClientUpload = () =>
  createWebApiClient({
    apiPrefix: `${API_PREFIX}/file`,
    requireTeamId: false,
  });

// 文件接口
export interface FileItem {
  id: string;
  name: string;
  type: string;
  size: number; // 文件大小（字节）
  link_num: number; // 被链接的知识库数量
  create_time: string; // 创建时间
  creator: {
    id: string;
    name: string;
    admin: boolean;
    in_team: boolean;
  };
  updator: {
    id: string;
    name: string;
    admin: boolean;
    in_team: boolean;
  };
  update_time: string; // 修改时间
  isFolder: boolean; // 是否是文件夹
  parentId: string | null; // 父文件夹ID，null表示根目录
  path: string; // 完整路径
  unlink_file_num?: number;
}

// 知识库引用接口
export interface KnowledgeBaseReference {
  id: string;
  name: string;
}

// 分片上传相关接口
export interface PreSignedUrlRes {
  upload_id: string;
  part_items: Array<{
    number: number;
    size: number;
    upload_url: string;
  }>;
}

export interface ETagPart {
  etag: string;
  number: number;
}

export interface ApiResponse<T> {
  data: T[];
}

/**
 * 获取文件列表
 * 分页查询
 */
export const useFiles = (
  pageNumber = 1,
  pageSize = 20,
  kbase_id?: string,
  scope?: string,
  tableset_id?: string,
  parent_id?: string | null
) => {
  return useQuery({
    queryKey: ["files", pageNumber, pageSize, parent_id],
    queryFn: async () => {
      const apiClient = await getApiClient();
      return apiClient.post<PaginatedResponse<FileItem>>(
        "/query",
        { kbase_id, scope, tableset_id, parent_id },
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      );
    },
    gcTime: 0,
  });
};

/**
 * 获取文件列表（无限滚动）
 * 支持文件夹导航和知识库过滤
 */
export const useInfiniteFiles = (
  kbase_id?: string,
  scope?: string,
  tableset_id?: string,
  parent_id?: string | null,
  pageSize = 20,
  enabled = true
) => {
  return useInfiniteQueryScroll({
    queryKey: [
      "infinite-files",
      parent_id || "root",
      kbase_id || "all",
      scope || "all",
      tableset_id || "all",
    ],
    queryFn: async (params: InfiniteScrollParams & { pageParam?: number }) => {
      const apiClient = await getApiClient();
      const response = await apiClient.post<PaginatedResponse<FileItem>>(
        "/query",
        { kbase_id, scope, tableset_id, parent_id },
        {
          isPaginated: true,
          pageNumber: params.pageNumber,
          pageSize: params.pageSize,
        }
      );
      return response;
    },
    refetchOnMount: "always",
    pageSize,
    enabled,
  });
};

/**
 * 获取文件夹内容
 */
export const useFolderContents = () => {
  return useMutation({
    mutationFn: async (data: { parentId: string | null }) => {
      const apiClient = await getApiClient();
      return apiClient.post<FileItem[]>("/folder-contents", data);
    },
  });
};

/**
 * 上传文件（支持多文件上传）
 * team_id 作为查询参数传递，body 中只包含 FormData
 */
export const useUploadFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: {
      formData: FormData;
      abortController?: AbortController;
      parentId?: string | null;
    }) => {
      // 获取当前团队ID
      const teamId = getTeamId();

      // 构建查询参数
      const queryParams = new URLSearchParams();
      if (teamId) {
        queryParams.append("team_id", teamId);
      }
      if (data.parentId) {
        queryParams.append("parent_id", data.parentId);
      }

      // 构建完整的URL
      const url = `/upload${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

      // 现在 fetch-client 会自动检测 FormData 并正确处理：
      // - 不会对 FormData 进行 JSON.stringify
      // - 不会设置 Content-Type（让浏览器自动设置）
      // - 不会在 body 中添加 team_id（因为我们通过查询参数传递）
      const apiClientUpload = await getApiClientUpload();
      return apiClientUpload.post<FileItem>(url, data.formData, {
        abortController: data.abortController,
        timeout: 300000, // 5分钟超时
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 流式上传文件
 */
export const useStreamUploadFile = () => {
  return useMutation({
    mutationFn: async (data: { file: File; parentId?: string | null }) => {
      const apiClient = await getApiClient();
      return apiClient.post<FileItem>("/stream-upload", data);
    },
  });
};

/**
 * 批量上传文件
 * team_id 作为查询参数传递，body 中只包含 FormData
 */
export const useUploadFiles = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (files: File[]) => {
      // 获取当前团队ID
      const teamId = getTeamId();

      // 构建查询参数
      const queryParams = new URLSearchParams();
      if (teamId) {
        queryParams.append("team_id", teamId);
      }

      // 构建完整的URL
      const url = `/upload${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

      const apiClientUpload = await getApiClientUpload();
      const uploadPromises = files.map((file) => {
        const formData = new FormData();
        formData.append("file", file);
        // fetch-client 会自动处理 FormData
        return apiClientUpload.post<FileItem>(url, formData);
      });

      return Promise.all(uploadPromises);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 创建文件夹
 */
export const useCreateFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { name: string; parentId: string | null }) => {
      const apiClient = await getApiClient();
      return apiClient.post<FileItem>("/create-folder", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 创建文件
 */
export const useCreateFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { upload_id: string; parent_id: string | null }) => {
      const apiClient = await getApiClient();
      return apiClient.post<FileItem>("/create", data);
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["files"],
      });
    },
  });
};

/**
 * 删除文件
 */
export const useDeleteFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { file_id: string }) => {
      const apiClient = await getApiClient();
      return apiClient.post("/delete", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 重命名文件
 */
export const useRenameFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { file_id: string; name: string }) => {
      const apiClient = await getApiClient();
      return apiClient.post<FileItem>("/modify", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 移动文件
 */
export const useMoveFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { file_id: string; parentId: string | null }) => {
      const apiClient = await getApiClient();
      return apiClient.post<FileItem>("/move", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 获取文件的知识库引用
 */
export const useFileReferences = () => {
  return useMutation({
    mutationFn: async (data: { file_id: string }) => {
      const apiClient = await getApiClient();
      return apiClient.post<KnowledgeBaseReference[]>("/references", data);
    },
  });
};

/**
 * 初始化分片上传
 */
export const useInitMultipartUpload = () => {
  return useMutation({
    mutationFn: async (data: { file: File; parentId?: string | null }) => {
      const apiClient = await getApiClient();
      return apiClient.post<ApiResponse<PreSignedUrlRes>>("/init-multipart-upload", {
        file_name: data.file.name,
        file_size: data.file.size,
        parent_id: data.parentId,
      });
    },
  });
};

/**
 * 完成分片上传
 */
export const useCompleteMultipartUpload = () => {
  const createFile = useCreateFile();
  return useMutation({
    mutationFn: async (data: {
      parentId: string | null;
      upload_id: string;
      part_etags: ETagPart[];
    }) => {
      const apiClient = await getApiClient();
      return apiClient.post("/complete-multipart-upload", data);
    },
    onSuccess: async (data, variables) => {
      if (variables?.upload_id) {
        await createFile.mutateAsync({
          upload_id: variables.upload_id,
          parent_id: variables?.parentId,
        });
      }
    },
  });
};

/**
 * 预览文件
 */
export const usePreviewFile = () => {
  return useMutation({
    mutationFn: async (data: { url: string; max_rows: number; max_columns: number }) => {
      const apiClient = await getApiClient();
      return apiClient.post<FileItem>("/table_preview", data);
    },
  });
};

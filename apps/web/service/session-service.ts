/**
 * session服务
 *
 * 提供session相关的API请求方法
 */

import { createWebApiClient, PaginatedResponse } from "@/lib/api/web-api-client";
import { useInfiniteQueryScroll } from "@ragtop-web/ui/hooks/use-infinite-scroll";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { API_PREFIX } from "./common";

// 创建API客户端
const getApiClient = () =>
  createWebApiClient({
    apiPrefix: `${API_PREFIX}/agent-session`,
  });

export interface Conversation {
  answers?: ChatMessage[];
  question?: ChatMessage;
  [property: string]: any;
}

/**
 * ChatMessage
 */
export interface ChatMessage {
  agent_id?: string;
  content?: string;
  create_time?: Date;
  id?: string;
  owner_id?: string;
  parent_id?: string;
  proto_ver?: number;
  session_id?: string;
  team_id?: string;
  type?: ChatType;
  [property: string]: any;
}

export interface Content {
  type: ContentType;
  content: string | FileContent | ReferenceContent | ReadyContnet;
}

export enum ContentType {
  Text = "TEXT",
  File = "FILE",
  Reference = "REFERENCE",
  Endmark = "END_MARK",
  Ready = "READY",
}

export interface FileContent {
  title: string;
  type: string;
  uri: string;
}

export interface ReferenceContent {
  total: number;
  chunks: IReferenceChunk[];
  doc_aggs: Docagg[];
}

export interface Docagg {
  count: number;
  doc_id: string;
  doc_name: string;
  url?: string;
}

export interface IReferenceChunk {
  id: string;
  content: null;
  document_id: string;
  document_name: string;
  dataset_id: string;
  image_id: string;
  similarity: number;
  vector_similarity: number;
  term_similarity: number;
  positions: number[];
}

export interface ReadyContnet {
  question_id: string;
  answer_id: string;
}

export enum ChatType {
  User = "USER",
  Agent = "AGENT",
}

export interface SessionCreateParams {
  agent_id: string;
  title: string;
}

export interface SessionUpdateParams {
  session_id: string;
  title?: string;
  agent_id?: string;
}

export interface Sessions {
  id: string;
  title: string;
  agent_id: string;
  create_time: number;
  update_time: number;
}

export interface Message {
  agent_id: string;
  session_id: string;
  text_content: string;
}

/**
 * 获取session列表
 * 分页查询
 */
export const useSessions = (agentId: string, pageNumber = 1, pageSize = 10) => {
  return useQuery({
    queryKey: ["sessions", agentId, pageNumber, pageSize],
    queryFn: async () => {
      const apiClient = await getApiClient();
      return apiClient.post<PaginatedResponse<Sessions>>(
        `/query`,
        { agent_id: agentId },
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      );
    },
    enabled: !!agentId,
  });
};

/**
 * 获取session列表 - 无限滚动版本
 * 使用 React Query 的 useInfiniteQuery
 */
export const useSessionsInfinite = (agentId: string, pageSize = 20, enabled = true) => {
  return useInfiniteQueryScroll<Sessions>({
    queryKey: ["sessions", "infinite", agentId],
    queryFn: async ({ pageNumber = 1, pageSize: size = pageSize }) => {
      const apiClient = await getApiClient();
      const response = await apiClient.post<PaginatedResponse<Sessions>>(
        `/query`,
        { agent_id: agentId },
        {
          isPaginated: true,
          pageNumber,
          pageSize: size,
        }
      );
      return {
        records: response.records || [],
        total: response.total || 0,
      };
    },
    pageSize,
    enabled: enabled && !!agentId,
    extraParams: { agentId },
  });
};

/**
 * 获取单个session详情
 */
export const useSession = (sessionId: string, enabled = true) => {
  return useQuery({
    queryKey: ["sessions", sessionId],
    queryFn: async () => {
      const apiClient = await getApiClient();
      return apiClient.post<Sessions>(`/describe`, { session_id: sessionId });
    },
    enabled: !!sessionId && enabled,
  });
};

/**
 * 获取session历史消息
 * 分页查询
 */
export const useSessionHistory = (
  sessionId: string,
  pageNumber = 1,
  pageSize = 20,
  enabled = true
) => {
  return useQuery({
    queryKey: ["sessions", sessionId, "history", pageNumber, pageSize],
    queryFn: async () => {
      const apiClient = await getApiClient();
      return apiClient.post<PaginatedResponse<Conversation>>(
        `/describe-history`,
        { session_id: sessionId },
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      );
    },
    enabled: !!sessionId && enabled,
    gcTime: 0,
  });
};

/**
 * 创建session
 */
export const useCreateSession = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SessionCreateParams) => {
      const apiClient = await getApiClient();
      return apiClient.post<Sessions>("/create", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["sessions"] });
    },
  });
};

/**
 * 更新session
 */
export const useUpdateSession = (agentId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SessionUpdateParams) => {
      const apiClient = await getApiClient();
      return apiClient.post<Sessions>(`/modify`, {
        ...data,
        agent_id: agentId,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["sessions"] });
      queryClient.invalidateQueries({ queryKey: ["agents"] });
    },
  });
};

/**
 * 删除session
 */
export const useDeleteSession = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: { session_id: string }) => {
      const apiClient = await getApiClient();
      return apiClient.post(`/delete`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["sessions", "infinite"] });
    },
  });
};

/**
 * 批量删除session
 */
export const useMultiDeleteSessions = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: { agent_id: string; session_ids: string[] }) => {
      const apiClient = await getApiClient();
      return apiClient.post(`/multi-delete`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["sessions", "infinite"] });
    },
  });
};

/**
 * 发送消息 (非流式)
 */
export const useSendMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: Message) => {
      const apiClient = await getApiClient();
      return apiClient.post(`/chat-completion`, data);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["sessions", variables.session_id, "history"],
      });
    },
  });
};

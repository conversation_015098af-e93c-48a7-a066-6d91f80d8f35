// 验证 i18n 配置脚本
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log("🔍 验证 i18n 配置...\n");

// 读取国际化文件
const zhPath = path.join(__dirname, "messages/zh.json");
const enPath = path.join(__dirname, "messages/en.json");

try {
  const zhMessages = JSON.parse(fs.readFileSync(zhPath, "utf8"));
  const enMessages = JSON.parse(fs.readFileSync(enPath, "utf8"));

  console.log("✅ 中文文件读取成功");
  console.log("✅ 英文文件读取成功\n");

  // 检查特定键名
  const testKeys = [
    "agent.delete.confirmTitle",
    "agent.delete.confirmDescription",
    "agent.common.loading",
    "agent.actions.edit",
    "agent.actions.delete",
  ];

  console.log("🔍 检查关键键名:\n");

  testKeys.forEach((key) => {
    const keys = key.split(".");
    let zhValue = zhMessages;
    let enValue = enMessages;

    for (const k of keys) {
      zhValue = zhValue?.[k];
      enValue = enValue?.[k];
    }

    console.log(`📝 ${key}:`);
    console.log(`   中文: ${zhValue || "❌ 未找到"}`);
    console.log(`   英文: ${enValue || "❌ 未找到"}\n`);
  });

  console.log("🎉 验证完成！");
} catch (error) {
  console.error("❌ 验证失败:", error.message);
}

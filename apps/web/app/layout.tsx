import { Auth<PERSON>heck } from "@/components/auth-check";
import { AuthInitializer } from "@/components/auth-initializer";
import ConfigWrapper from "@/components/config-provider-server";
import { Providers } from "@/components/providers";
import { SiteConfigProvider } from "@/components/site-config-provider";
import "@ragtop-web/ui/globals.css";
import { getLocale, getMessages } from "next-intl/server";
import { connection } from "next/server";

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  await connection();

  // Get locale and messages for client components
  const locale = await getLocale();
  const messages = await getMessages();

  const cfg = {
    API_PREFIX: process.env.API_PREFIX ?? "/api/v1/portal-ragtop",
    WEB_API_URL: process.env.WEB_API_URL ?? "",
    WEB_INNER_API_URL: process.env.WEB_INNER_API_URL ?? "",
    SHOW_NL2CODE: process.env.SHOW_NL2CODE ?? "true",
    UPLOAD_MODE: process.env.UPLOAD_MODE ?? "",
    FILE_SIZE_LIMIT: process.env.FILE_SIZE_LIMIT ?? "100",
    TABLE_FILE_SIZE_LIMIT: process.env.TABLE_FILE_SIZE_LIMIT ?? "50",
    FILE_PREVIEW_MAX_ROWS: process.env.TABLE_FILE_SIZE_LIMIT ?? "1000",
  };
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body className={`font-sans antialiased`}>
        <ConfigWrapper cfg={cfg}>
          <Providers locale={locale} messages={messages}>
            <SiteConfigProvider>
              <AuthInitializer>
                <AuthCheck>
                  <div className="relative flex min-h-svh flex-col">
                    <main className="flex-1">{children}</main>
                  </div>
                </AuthCheck>
              </AuthInitializer>
            </SiteConfigProvider>
          </Providers>
        </ConfigWrapper>
      </body>
    </html>
  );
}

"use client";

import { useDatasetSchemasInfinite } from "@/service/dataset-service";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@ragtop-web/ui/components/collapsible";
import { InfiniteScrollList } from "@ragtop-web/ui/components/infinite-scroll-list";
import { SidebarGroup, SidebarGroupLabel } from "@ragtop-web/ui/components/sidebar";
import { ChevronRight } from "lucide-react";
import TableList from "./table-list";

export default function SchemaList({
  datasetId,
  onTableSelect,
  onSchemaSelect,
  originalParams,
  expandedTables,
  setExpandedTables,
  onTableList,
}: {
  datasetId: string;
  onTableSelect: any;
  onSchemaSelect: any;
  originalParams: any;
  expandedTables: string[];
  setExpandedTables: (expandedTables: string[]) => void;
  onTableList: (tableList: any[]) => void;
}) {
  const {
    data,
    isLoading,
    hasMore,
    isInitialLoading,
    isFetchingNextPage,
    error,
    fetchNextPage,
    reload,
  } = useDatasetSchemasInfinite(datasetId, 10);

  const toggleAgentExpanded = (schema_name: string) => {
    if (expandedTables.includes(schema_name)) {
      onSchemaSelect({
        schema_name: undefined,
        tableset_id: datasetId,
        expanded: false,
      });
      setExpandedTables(expandedTables.filter((name) => name !== schema_name));
    } else {
      onSchemaSelect({
        schema_name: schema_name,
        tableset_id: datasetId,
        expanded: true,
      });
      setExpandedTables([...expandedTables, schema_name]);
    }
  };

  return (
    <div className="col-span-3 border-r pr-4">
      <div className="h-full space-y-2">
        <h3 className="text-muted-foreground mb-2 text-sm font-medium">数据库结构</h3>
        {isLoading ? (
          <div className="flex h-32 items-center justify-center">
            <p className="text-muted-foreground text-sm">加载中...</p>
          </div>
        ) : (
          <div className="h-[580px] space-y-1 overflow-y-auto">
            <InfiniteScrollList
              data={data}
              isLoading={isLoading}
              hasMore={hasMore}
              isInitialLoading={isInitialLoading}
              isFetchingNextPage={isFetchingNextPage}
              error={error}
              fetchNextPage={fetchNextPage}
              reload={reload}
              renderItem={(schema) => (
                <Collapsible
                  key={schema.schema_name}
                  title={schema.schema_name}
                  asChild
                  className="group/collapsible"
                  open={expandedTables.includes(schema.schema_name || "")}
                >
                  <SidebarGroup>
                    <SidebarGroupLabel
                      asChild
                      className="group/label text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground text-sm"
                    >
                      <CollapsibleTrigger
                        onClick={() => toggleAgentExpanded(schema.schema_name || "")}
                      >
                        {schema.schema_name}
                        <ChevronRight className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90" />
                      </CollapsibleTrigger>
                    </SidebarGroupLabel>

                    <CollapsibleContent>
                      <TableList
                        schemaName={schema?.schema_name || ""}
                        onTableSelect={onTableSelect}
                        datasetId={datasetId}
                        originalParams={originalParams}
                        onTableList={onTableList}
                      />
                    </CollapsibleContent>
                  </SidebarGroup>
                </Collapsible>
              )}
              renderLoadDone={() => <></>}
            />
          </div>
        )}
      </div>
    </div>
  );
}

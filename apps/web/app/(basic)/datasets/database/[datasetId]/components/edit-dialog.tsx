"use client";

import { useDatasetDocuments, useUpdateDocument } from "@/service/dataset-service";
import { Button } from "@ragtop-web/ui/components/button";
import {
  Di<PERSON>,
  DialogBody,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import { Textarea } from "@ragtop-web/ui/components/textarea";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

export default function EditDialog({
  open,
  onClose,
  setColumnLists,
  originalParams,
}: {
  open: boolean;
  onClose: () => void;
  setColumnLists: any;
  originalParams?: {
    column_name?: string;
    schema_name?: string;
    table_name?: string;
    tableset_id?: string;
    annotation?: string;
  };
}) {
  const { toast } = useToast();
  const t = useTranslations("forms.placeholders");

  const [documentDescription, setDocumentDescription] = useState("");
  const updateDocumentMutation = useUpdateDocument();
  const getDocumentsMutation = useDatasetDocuments();

  // 保存文档描述
  const handleSaveDescription = () => {
    if (!documentDescription) return;

    updateDocumentMutation.mutate(
      {
        ...originalParams,
        annotation: documentDescription,
      },
      {
        onSuccess: () => {
          toast({
            title: "保存成功",
            description: "文档描述已更新",
          });
          if (originalParams?.column_name) {
            getDocumentsMutation.mutate(originalParams, {
              onSuccess: (data) => {
                setColumnLists(data);
                onClose();
                setDocumentDescription("");
              },
            });
          } else {
            onClose();
            setDocumentDescription("");
          }
        },
        onError: (err) => {
          toast({
            title: "保存失败",
            description: "更新文档描述时发生错误",
            variant: "destructive",
          });
        },
      }
    );
  };

  useEffect(() => {
    if (originalParams?.annotation) {
      setDocumentDescription(originalParams?.annotation);
    }
  }, [originalParams?.annotation]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>编辑</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <div className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="description" className="mb-2 block text-sm font-medium">
                描述
              </label>
              <Textarea
                id="description"
                placeholder={t("description")}
                value={documentDescription}
                onChange={(e) => setDocumentDescription(e.target.value)}
                rows={5}
              />
            </div>
          </div>
        </DialogBody>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button onClick={handleSaveDescription} disabled={updateDocumentMutation.isPending}>
            {updateDocumentMutation.isPending ? "保存中..." : "保存"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

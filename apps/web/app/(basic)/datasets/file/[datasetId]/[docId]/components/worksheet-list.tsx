"use client";

import { But<PERSON> } from "@ragtop-web/ui/components/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@ragtop-web/ui/components/collapsible";
import { SidebarGroup, SidebarGroupLabel } from "@ragtop-web/ui/components/sidebar";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@ragtop-web/ui/components/tooltip";
import { ChevronRight, FileText, Table } from "lucide-react";

interface ColumnData {
  name: string;
  range: string;
  data_type: string;
  semantic_name: string | null;
  semantic_comment: string | null;
}

interface SubtableData {
  range: string;
  title: string | null;
  title_range: string;
  footnote: string | null;
  footnote_range: string | null;
  data_range: string;
  columns: ColumnData[];
}

interface WorksheetData {
  name?: string;
  sheet_name?: string;
  semantic_name: string | null;
  semantic_comment: string | null;
  subtables: SubtableData[];
}

interface NavigationState {
  worksheetIndex?: number;
  subtableIndex?: number;
}

export default function WorksheetList({
  worksheets,
  navigationState,
  onWorksheetSelect,
  onSubtableSelect,
}: {
  worksheets: WorksheetData[];
  navigationState: NavigationState;
  onWorksheetSelect: (worksheetIndex: number) => void;
  onSubtableSelect: (worksheetIndex: number, subtableIndex: number) => void;
}) {
  return (
    <div className="col-span-3 border-r pr-4">
      <div className="h-full space-y-2">
        <h3 className="text-muted-foreground mb-2 text-sm font-medium">File</h3>
        <div className="h-[580px] space-y-1 overflow-y-auto">
          <TooltipProvider>
            {worksheets.map((worksheet, worksheetIndex) => {
              const worksheetName =
                worksheet.name || worksheet.sheet_name || `Worksheet ${worksheetIndex + 1}`;
              const isWorksheetExpanded = navigationState.worksheetIndex === worksheetIndex;

              return (
                <Collapsible
                  key={worksheetIndex}
                  title={worksheetName}
                  asChild
                  className="group/collapsible mb-0 p-0"
                  open={isWorksheetExpanded}
                >
                  <SidebarGroup>
                    <SidebarGroupLabel
                      asChild
                      className="group/label text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground text-sm"
                    >
                      <CollapsibleTrigger onClick={() => onWorksheetSelect(worksheetIndex)}>
                        <FileText className="mr-2 h-4 w-4" />
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span
                              className="inline-block max-w-[160px] truncate align-middle overflow-ellipsis whitespace-nowrap"
                              tabIndex={0}
                              aria-label={worksheetName}
                            >
                              {`${worksheetName}(Sheet)`}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent side="right">{`${worksheetName}(Sheet)`}</TooltipContent>
                        </Tooltip>
                        {worksheet.subtables.length > 0 && (
                          <ChevronRight className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90" />
                        )}
                      </CollapsibleTrigger>
                    </SidebarGroupLabel>

                    {worksheet.subtables.length > 0 && (
                      <CollapsibleContent>
                        <div className="ml-6 space-y-1">
                          {worksheet.subtables.map((subtable, subtableIndex) => {
                            const subtableName = subtable.title || `Subtable ${subtableIndex + 1}`;
                            const isSubtableSelected =
                              navigationState.worksheetIndex === worksheetIndex &&
                              navigationState.subtableIndex === subtableIndex;

                            return (
                              <Button
                                key={subtableIndex}
                                variant={isSubtableSelected ? "secondary" : "ghost"}
                                size="sm"
                                className="h-8 w-full justify-start text-sm"
                                onClick={() => onSubtableSelect(worksheetIndex, subtableIndex)}
                              >
                                <Table className="mr-2 h-3 w-3" />
                                {subtableName}
                              </Button>
                            );
                          })}
                        </div>
                      </CollapsibleContent>
                    )}
                  </SidebarGroup>
                </Collapsible>
              );
            })}
          </TooltipProvider>
        </div>
      </div>
    </div>
  );
}

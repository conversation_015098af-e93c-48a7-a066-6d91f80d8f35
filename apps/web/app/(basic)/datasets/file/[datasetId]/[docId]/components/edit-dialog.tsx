"use client";

import { But<PERSON> } from "@ragtop-web/ui/components/button";
import {
  Dialog,
  DialogBody,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import { Input } from "@ragtop-web/ui/components/input";
import { Textarea } from "@ragtop-web/ui/components/textarea";
import { useEffect, useState } from "react";

export default function EditDialog({
  open,
  onClose,
  semantic_name,
  semantic_comment,
  onSave,
  type = "worksheet",
}: {
  open: boolean;
  onClose: () => void;
  semantic_name: string | undefined;
  semantic_comment: string | undefined;
  onSave: (semantic_name: string | undefined, semantic_comment: string | undefined) => void;
  type?: "worksheet" | "column";
}) {
  const [aliasValue, setAliasValue] = useState("");
  const [commentValue, setCommentValue] = useState("");

  // label/placeholder/help 文本根据 type 切换
  const aliasLabel = type === "worksheet" ? "Sheet别名" : "列名";
  const aliasPlaceholder = type === "worksheet" ? "请输入Sheet别名..." : "请输入列名...";
  const aliasHelp =
    type === "worksheet" ? "为该Sheet提供一个更易理解的别名" : "为该列提供一个更易理解的别名";
  const commentLabel = type === "worksheet" ? "Sheet描述" : "列描述";
  const commentPlaceholder = type === "worksheet" ? "请输入Sheet描述..." : "请输入列描述...";
  const commentHelp =
    type === "worksheet"
      ? "详细描述该Sheet的含义、用途或相关信息"
      : "详细描述该列的含义、用途或相关信息";

  // 处理保存
  const handleSave = () => {
    onSave(aliasValue.trim() || undefined, commentValue.trim() || undefined);
  };

  // 重置表单
  const handleClose = () => {
    setAliasValue("");
    setCommentValue("");
    onClose();
  };

  useEffect(() => {
    if (open) {
      setAliasValue(semantic_name || "");
      setCommentValue(semantic_comment || "");
    }
  }, [open, semantic_name, semantic_comment]);

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>编辑</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <div className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="semantic_name" className="block text-sm font-medium">
                {aliasLabel}
              </label>
              <Input
                id="semantic_name"
                placeholder={aliasPlaceholder}
                value={aliasValue}
                onChange={(e) => setAliasValue(e.target.value)}
              />
              <p className="text-muted-foreground text-xs">{aliasHelp}</p>
            </div>

            <div className="space-y-2">
              <label htmlFor="semantic_comment" className="block text-sm font-medium">
                {commentLabel}
              </label>
              <Textarea
                id="semantic_comment"
                placeholder={commentPlaceholder}
                value={commentValue}
                onChange={(e) => setCommentValue(e.target.value)}
                rows={4}
              />
              <p className="text-muted-foreground text-xs">{commentHelp}</p>
            </div>
          </div>
        </DialogBody>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            取消
          </Button>
          <Button onClick={handleSave}>保存</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

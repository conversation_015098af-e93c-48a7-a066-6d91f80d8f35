"use client";

import { Sheet } from "@/service/dataset-service";
import { Button } from "@ragtop-web/ui/components/button";
import { DataTable, type ColumnDef } from "@ragtop-web/ui/components/data-table";
import { Tooltip, TooltipContent, TooltipTrigger } from "@ragtop-web/ui/components/tooltip";
import { Pencil } from "lucide-react";
import { useState } from "react";
import EditDialog from "./edit-dialog";

interface ColumnData {
  original_name?: string;
  name?: string;
  range: string;
  data_type: string;
  semantic_name: string | null;
  semantic_comment: string | null;
}

interface NavigationState {
  worksheetIndex?: number;
  subtableIndex?: number;
}

interface EditTarget {
  type: "worksheet" | "column";
  worksheetIndex: number;
  subtableIndex?: number;
  columnIndex?: number;
  semantic_name: string | undefined;
  semantic_comment: string | undefined;
}

interface WorksheetContentProps {
  worksheets: Sheet[];
  navigationState: NavigationState;
  onWorksheetUpdate: (updatedWorksheets: Sheet[]) => void;
}

export default function WorksheetContent({
  worksheets,
  navigationState,
  onWorksheetUpdate,
}: WorksheetContentProps) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editTarget, setEditTarget] = useState<EditTarget | null>(null);

  if (navigationState.worksheetIndex === undefined) {
    return null;
  }

  const worksheet = worksheets[navigationState.worksheetIndex];
  if (!worksheet) {
    return null;
  }

  // 处理编辑
  const handleEdit = (target: EditTarget) => {
    setEditTarget(target);
    setIsEditDialogOpen(true);
  };

  // 处理保存编辑
  const handleSaveEdit = (
    semantic_name: string | undefined,
    semantic_comment: string | undefined
  ) => {
    if (!editTarget) return;

    // 深拷贝工作表数据
    const updatedWorksheets = worksheets.map((ws, wsIndex) => {
      if (wsIndex !== editTarget.worksheetIndex) {
        return ws;
      }

      if (editTarget.type === "worksheet") {
        // 更新工作表的semantic字段
        return {
          ...ws,
          semantic_name: semantic_name ?? undefined,
          semantic_comment: semantic_comment ?? undefined,
        };
      } else if (
        editTarget.type === "column" &&
        typeof editTarget.subtableIndex === "number" &&
        typeof editTarget.columnIndex === "number"
      ) {
        // 更新列的semantic字段
        return {
          ...ws,
          subtables: (ws.subtables ?? []).map((st, stIndex) => {
            if (stIndex !== editTarget.subtableIndex) {
              return st;
            }
            return {
              ...st,
              columns: (st.columns ?? []).map((col, colIndex) => {
                if (colIndex !== editTarget.columnIndex) {
                  return col;
                }
                return {
                  ...col,
                  name: semantic_name ?? undefined,
                  semantic_name: semantic_name ?? undefined,
                  semantic_comment: semantic_comment ?? undefined,
                };
              }),
            };
          }),
        };
      }

      return ws;
    });

    // 更新父组件的工作表数据
    onWorksheetUpdate(updatedWorksheets);
    setIsEditDialogOpen(false);
    setEditTarget(null);
  };

  // 如果选择了subtable，显示列信息
  if (navigationState.subtableIndex !== undefined) {
    const subtable = worksheet.subtables?.[navigationState.subtableIndex];
    if (!subtable) return null;

    const columns: ColumnDef<ColumnData>[] = [
      {
        accessorKey: "original_name",
        header: "原文件列名",
        cell: ({ row }) => {
          const column = row.original;
          return <span className="font-mono text-sm">{column.original_name}</span>;
        },
      },
      {
        accessorKey: "data_type",
        header: "数据类型",
        cell: ({ row }) => {
          const column = row.original;
          return (
            <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-blue-700/10 ring-inset">
              {column.data_type}
            </span>
          );
        },
      },
      {
        accessorKey: "semantic_name",
        header: "列名",
        cell: ({ row }) => {
          const column = row.original;

          return (
            <div className="flex items-center justify-between">
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="max-w-[200px] truncate">
                    {column.semantic_name || column.name || "-"}
                  </div>
                </TooltipTrigger>
                <TooltipContent side="left" className="max-h-[400px] overflow-y-auto">
                  <p className="max-w-[300px] break-words whitespace-normal">
                    {column.semantic_name || column.name || "-"}
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
          );
        },
      },
      {
        accessorKey: "semantic_comment",
        header: "列描述",
        cell: ({ row }) => {
          const column = row.original;
          return (
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="max-w-[200px] truncate">{column.semantic_comment || "-"}</div>
              </TooltipTrigger>
              <TooltipContent side="left" className="max-h-[400px] overflow-y-auto">
                <p className="max-w-[300px] break-words whitespace-normal">
                  {column.semantic_comment || "-"}
                </p>
              </TooltipContent>
            </Tooltip>
          );
        },
      },
      // 操作列
      {
        id: "actions",
        header: "操作",
        cell: ({ row }) => {
          const column = row.original;
          const columnIndex = subtable.columns?.findIndex((c) => c.name === column.name);
          return (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  aria-label="编辑"
                  tabIndex={0}
                  onClick={() =>
                    handleEdit({
                      type: "column",
                      worksheetIndex: navigationState.worksheetIndex!,
                      subtableIndex: navigationState.subtableIndex!,
                      columnIndex,
                      semantic_name: column.semantic_name || column.name || undefined,
                      semantic_comment: column?.semantic_comment || undefined,
                    })
                  }
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      handleEdit({
                        type: "column",
                        worksheetIndex: navigationState.worksheetIndex!,
                        subtableIndex: navigationState.subtableIndex!,
                        columnIndex,
                        semantic_name: column.semantic_name || column.name || undefined,
                        semantic_comment: column?.semantic_comment || undefined,
                      });
                    }
                  }}
                >
                  <Pencil className="h-4 w-4" />
                  <span className="sr-only">编辑</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>编辑</p>
              </TooltipContent>
            </Tooltip>
          );
        },
      },
    ];

    return (
      <>
        <div className="h-full overflow-hidden">
          <DataTable columns={columns} data={subtable.columns ?? []} />
        </div>
        {isEditDialogOpen && editTarget && (
          <EditDialog
            open={isEditDialogOpen}
            onClose={() => {
              setIsEditDialogOpen(false);
              setEditTarget(null);
            }}
            semantic_name={editTarget.semantic_name}
            semantic_comment={editTarget.semantic_comment}
            onSave={handleSaveEdit}
            type={editTarget.type}
          />
        )}
      </>
    );
  }

  // 显示Sheet信息和subtables列表

  return (
    <>
      {/* Sheet信息 */}
      <div className="bg-muted/30 space-y-3 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Sheet信息</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              handleEdit({
                type: "worksheet",
                worksheetIndex: navigationState.worksheetIndex!,
                semantic_name: worksheet.semantic_name,
                semantic_comment: worksheet.semantic_comment,
              })
            }
          >
            <Pencil className="mr-2 h-4 w-4" />
            编辑
          </Button>
        </div>
        <div className="grid grid-cols-2 gap-x-8 gap-y-4 text-sm">
          <div>
            <span className="text-muted-foreground font-medium">名称:</span>
            <div>{worksheet.name || worksheet.sheet_name || "未命名"}</div>
          </div>
          <div>
            <span className="text-muted-foreground font-medium">子表数量:</span>
            <div>{worksheet.subtables?.length || 0}</div>
          </div>
          <div>
            <span className="text-muted-foreground font-medium">Sheet别名:</span>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="block max-h-[4.5em] overflow-hidden whitespace-pre-line">
                  {worksheet.semantic_name || "-"}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <span className="block max-w-[500px] whitespace-pre-line">
                  {worksheet.semantic_name || "-"}
                </span>
              </TooltipContent>
            </Tooltip>
          </div>
          <div>
            <span className="text-muted-foreground font-medium">Sheet描述:</span>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="block max-h-[4.5em] overflow-hidden whitespace-pre-line">
                  {worksheet.semantic_comment || "-"}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <span className="block max-w-[500px] whitespace-pre-line">
                  {worksheet.semantic_comment || "-"}
                </span>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
      </div>
      {isEditDialogOpen && editTarget && (
        <EditDialog
          open={isEditDialogOpen}
          onClose={() => {
            setIsEditDialogOpen(false);
            setEditTarget(null);
          }}
          semantic_name={editTarget.semantic_name}
          semantic_comment={editTarget.semantic_comment}
          onSave={handleSaveEdit}
          type={editTarget.type}
        />
      )}
    </>
  );
}

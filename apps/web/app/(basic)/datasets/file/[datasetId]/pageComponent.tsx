"use client";
import { truncateFileName } from "@/app/(basic)/files/utils";
import { getFileIcon } from "@/lib/file-utils";
import { checkIsOwner } from "@/lib/user-role";
import { formatDateTime } from "@/lib/utils";
import {
  RunStatus,
  useAddFileSetDocuments,
  useFileset,
  useFilesetDocDelete,
  useFilesetDocList,
  useFilesetDocReload,
  useFilesetDocStartParse,
  useFilesetDocStopParse,
} from "@/service/dataset-service";
import {
  KnowledgeBaseDocList,
  type KnowledgeBaseDocList as FilesetDocList,
} from "@/service/knowledge-base-doc-service";
import { Badge } from "@ragtop-web/ui/components/badge";
import { Button } from "@ragtop-web/ui/components/button";
import { Checkbox } from "@ragtop-web/ui/components/checkbox";
import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { createStickyColumn, type ColumnDef } from "@ragtop-web/ui/components/data-table";
import { IconButton } from "@ragtop-web/ui/components/icon-button";
import { PaginatedTable } from "@ragtop-web/ui/components/paginated-table";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { usePagination } from "@ragtop-web/ui/hooks/use-pagination";
import { useBatchOperations, useTableSelection } from "@ragtop-web/ui/hooks/use-table-selection";
import { Loader2, Play, PlusIcon, RefreshCw, Settings, Square, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ReactElement, useEffect, useState } from "react";
import { FileSelectionDrawer } from "./components/file-selection-drawer";

// 解析状态类型
export type ParseStatus = RunStatus;

// 渲染状态图标
function renderStatusBadge(status: string, t: (key: string) => string): ReactElement {
  switch (status) {
    case RunStatus.Done:
      return (
        <Badge variant="default" className="border-green-200 bg-green-50 text-green-700">
          {t("status.parseSuccess")}
        </Badge>
      );
    case RunStatus.Fail:
      return <Badge variant="destructive">{t("status.parseFailed")}</Badge>;
    case RunStatus.Running:
      return (
        <Badge variant="outline" className="border-amber-200 bg-amber-50 text-amber-700">
          {t("status.parsing")}
        </Badge>
      );
    case RunStatus.Unstart:
      return <Badge variant="outline">{t("status.unparsed")}</Badge>;
    case RunStatus.Cancelling:
      return <Badge variant="outline">{t("status.cancelling")}</Badge>;
    case RunStatus.Cancel:
      return <Badge variant="outline">{t("status.cancelled")}</Badge>;
    default:
      return <Badge variant="outline">{t("status.unknown")}</Badge>;
  }
}

export default function FilesetDetails({ datasetId }: { datasetId: string }) {
  const [isInitialized, setIsInitialized] = useState(false);
  const router = useRouter();
  const t = useTranslations("datasets.fileDetail");
  const statusT = useTranslations("knowledgeBase");

  // 删除确认对话框状态
  const [fileToDelete, setFileToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);

  // 重置解析确认对话框状态
  const [fileToReset, setFileToReset] = useState<{
    id: string;
    name: string;
  } | null>(null);

  // 批量删除确认对话框状态
  const [batchDeleteConfirm, setBatchDeleteConfirm] = useState<{
    count: number;
  } | null>(null);

  // 文件选择抽屉状态
  const [isFileSelectionDrawerOpen, setIsFileSelectionDrawerOpen] = useState(false);
  // 添加状态来跟踪当前正在操作的文件ID
  const [operatingFileId, setOperatingFileId] = useState<string | null>(null);

  // 选择状态管理
  const selection = useTableSelection<KnowledgeBaseDocList>();

  const batchOps = useBatchOperations(selection.selectedRows);

  const {
    pageNumber,
    pageSize,
    handlePageChange: originalHandlePageChange,
    handlePageSizeChange: originalHandlePageSizeChange,
  } = usePagination({
    initialPageSize: 10,
  });

  // 自定义页面切换处理函数，切换时清空选择状态
  const handlePageChange = (page: number) => {
    selection.clearSelection();
    originalHandlePageChange(page);
  };

  // 自定义页面大小切换处理函数，切换时清空选择状态
  const handlePageSizeChange = (size: number) => {
    selection.clearSelection();
    originalHandlePageSizeChange(size);
  };

  // Toast提示
  const { toast } = useToast();

  // 获取知识库数据 - 延迟加载
  const { data, isLoading } = useFilesetDocList(datasetId, pageNumber, pageSize, isInitialized);
  const { data: fileset, isError } = useFileset(datasetId, isInitialized);

  // 页面初始化时触发数据加载
  useEffect(() => {
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (isError) {
      router.push("/");
    }
  }, [isError]);

  // API hooks
  const startParseMutation = useFilesetDocStartParse();
  const stopParseMutation = useFilesetDocStopParse();
  // 暂时不支持文件
  // const modifyChunkingMutation = useKBaseDocModifyChunking()
  const addDocumentsMutation = useAddFileSetDocuments();
  const deleteDocumentsMutation = useFilesetDocDelete();
  const reloadMutation = useFilesetDocReload();

  // 处理批量启用/禁用
  const handleBatchParse = async (enable: boolean) => {
    await batchOps.executeBatch(async (rows) => {
      if (enable) {
        const enableStart = rows.reduce((acc, row) => {
          if (![RunStatus.Running, RunStatus.Cancelling].includes(row.run_status)) {
            acc.push(row.id);
          }

          return acc;
        }, [] as string[]);

        if (enableStart.length === 0) {
          toast({
            title: t("messages.error"),
            description: t("actions.batchParseError"),
            variant: "destructive",
          });

          return;
        }

        return startParseMutation.mutate(
          {
            document_ids: enableStart,
          },
          {
            onSuccess: () => {
              toast({
                title: t("messages.success"),
                description: `${enableStart?.length} ${t("messages.parseQueueSuccess")}`,
              });
              selection.clearSelection();
            },
            onError: () => {
              toast({
                title: t("messages.error"),
                description: t("actions.batchOperationFailed"),
                variant: "destructive",
              });
            },
          }
        );
      } else {
        const enableStop = rows.reduce((acc, row) => {
          if ([RunStatus.Running].includes(row.run_status)) {
            acc.push(row.id);
          }

          return acc;
        }, [] as string[]);

        if (enableStop.length === 0) {
          toast({
            title: t("messages.error"),
            description: t("actions.batchStopError"),
            variant: "destructive",
          });

          return;
        }

        return stopParseMutation.mutate(
          {
            document_ids: enableStop,
          },
          {
            onSuccess: () => {
              toast({
                title: t("messages.success"),
                description: `${enableStop?.length} ${t("messages.stopParseSuccess")}`,
              });
              selection.clearSelection();
            },
            onError: () => {
              toast({
                title: t("messages.error"),
                description: t("actions.batchOperationFailed"),
                variant: "destructive",
              });
            },
          }
        );
      }
    });
  };

  // 处理添加新文档
  const handleAddNewDocument = () => {
    setIsFileSelectionDrawerOpen(true);
  };

  // 处理文件选择确认
  const handleFileSelectionConfirm = async (selectedFileIds: string[]) => {
    if (!selectedFileIds.length) {
      return;
    }

    try {
      await addDocumentsMutation.mutateAsync({
        tableset_id: datasetId,
        file_ids: selectedFileIds,
      });

      toast({
        title: "添加成功",
        description: `已成功添加 ${selectedFileIds.length} 个文件到知识库`,
      });

      setIsFileSelectionDrawerOpen(false);
    } catch {
      toast({
        title: "添加失败",
        description: "添加文件时发生错误，请重试",
        variant: "destructive",
      });
    }
  };

  // 处理解析文件
  const handleParseFile = async (fileId: string, currentStatus: ParseStatus) => {
    if (!fileset) {
      return;
    }

    setOperatingFileId(fileId);
    try {
      // 根据当前状态决定操作
      if (currentStatus === RunStatus.Running) {
        // 停止解析
        await stopParseMutation.mutateAsync({
          // kbase_id: datasetId,
          document_ids: [fileId],
        });
        toast({
          title: "成功",
          description: "已停止解析文件",
        });
      } else {
        // 开始解析
        await startParseMutation.mutateAsync({
          // kbase_id: datasetId,
          document_ids: [fileId],
        });
        toast({
          title: "成功",
          description: "已开始解析文件",
        });
      }
    } catch {
      toast({
        title: "错误",
        description: "操作失败，请重试",
        variant: "destructive",
      });
    } finally {
      setOperatingFileId(null);
    }
  };

  // 处理删除文件
  const handleDeleteFile = (fileId: string, fileName: string) => {
    setFileToDelete({ id: fileId, name: fileName });
  };

  // 确认删除文件
  const confirmDeleteFile = () => {
    if (!fileToDelete) {
      return;
    }
    deleteDocumentsMutation.mutate(
      {
        document_ids: [fileToDelete.id],
      },
      {
        onSuccess: () => {
          toast({
            title: "成功",
            description: "已删除文件",
          });
          setFileToDelete(null);
        },
        onError: () => {
          toast({
            title: "错误",
            description: "删除文件时发生错误，请重试",
            variant: "destructive",
          });
        },
      }
    );
  };

  const handleBatchDelete = () => {
    if (selection.selectedRows.length === 0) {
      return;
    }
    const selectedRows = selection.selectedRows.filter((row) =>
      checkIsOwner(row.creator?.id, row.creator?.admin)
    );

    if (selectedRows.length === 0) {
      toast({
        title: "错误",
        description: "您没有删除这些文件的权限",
        variant: "destructive",
      });

      return;
    }
    setBatchDeleteConfirm({ count: selectedRows.length });
  };

  const confirmBatchDelete = () => {
    if (!batchDeleteConfirm) {
      return;
    }
    deleteDocumentsMutation.mutate(
      { document_ids: selection.selectedRows.map((row) => row.id) },
      {
        onSuccess: () => {
          toast({
            title: "成功",
            description: `已批量删除 ${batchDeleteConfirm.count} 个文件`,
          });
          selection.clearSelection();
          setBatchDeleteConfirm(null);
        },
        onError: () => {
          toast({
            title: "错误",
            description: "批量删除失败，请重试",
            variant: "destructive",
          });
        },
      }
    );
  };

  // 处理重置解析
  const confirmResetParse = () => {
    if (!fileToReset) {
      return;
    }
    reloadMutation.mutate(
      { doc_id: fileToReset.id, tableset_id: datasetId },
      {
        onSuccess: () => {
          toast({
            title: "重置成功",
            description: `文件 \"${fileToReset.name}\" 的解析已重置`,
          });
          setFileToReset(null);
        },
        onError: () => {
          toast({
            title: "重置失败",
            description: "重置解析时发生错误，请重试",
            variant: "destructive",
          });
        },
      }
    );
  };

  if (isError) {
    return (
      <CustomContainer title="文件数据集详情">
        <div className="flex h-64 items-center justify-center">
          <p className="text-muted-foreground">未找到指定的文件数据集</p>
        </div>
      </CustomContainer>
    );
  }

  if (!fileset) {
    return (
      <CustomContainer title="文件数据集详情">
        <div className="flex h-64 items-center justify-center">
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </CustomContainer>
    );
  }

  // 构建面包屑导航
  const breadcrumbs = [
    {
      title: "文件数据集",
      href: "/datasets",
      isCurrent: false,
      linkComponent: Link,
    },
    {
      title: fileset.name || "文件数据集详情",
      href: `/file/${datasetId}`,
      isCurrent: true,
    },
  ];

  const columns: ColumnDef<FilesetDocList>[] = [
    createStickyColumn(
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      "left",
      50
    ),
    createStickyColumn(
      {
        accessorKey: "name",
        header: t("fileName"),
        cell: ({ row }) => (
          <div className="flex w-full items-center gap-2 overflow-hidden font-medium">
            <div className="flex-shrink-0">{getFileIcon(row.original.type || "")}</div>
            <span className="truncate">{truncateFileName(row.original.name, 25)}</span>
          </div>
        ),
      },
      "left",
      300
    ),
    {
      accessorKey: "run_status",
      header: t("status"),
      cell: ({ row }) => renderStatusBadge(row.original.run_status, statusT),
    },
    {
      accessorKey: "owner_name",
      header: t("createdBy"),
      cell: ({ row }) => row.original.creator?.name,
    },
    {
      accessorKey: "create_time",
      header: t("createTime2"),
      cell: ({ row }) => formatDateTime(row?.original?.create_time || ""),
    },
    {
      accessorKey: "update_name",
      header: t("updatedBy"),
      cell: ({ row }) => row.original.updator?.name,
    },
    {
      accessorKey: "update_time",
      header: t("updateTime"),
      cell: ({ row }) => formatDateTime(row?.original?.update_time || ""),
    },

    createStickyColumn(
      {
        id: "actions",
        header: () => <div className="text-right">操作</div>,
        cell: ({ row }) => {
          const file = row.original;
          const creator = file?.creator;
          const isRowOwner = checkIsOwner(creator?.id, creator?.admin);

          return (
            <div className="flex items-center justify-end gap-1">
              <IconButton
                variant="ghost"
                tooltip={
                  file.run_status === RunStatus.Cancelling
                    ? "取消中，不能执行解析操作"
                    : file.run_status === RunStatus.Running
                      ? "解析中暂不支持停止解析"
                      : file.run_status === RunStatus.Fail
                        ? "重试解析"
                        : "解析文件"
                }
                onClick={() => {
                  handleParseFile(file.id, file.run_status);
                }}
                disabled={[RunStatus.Running, RunStatus.Cancelling].includes(file.run_status)}
              >
                {operatingFileId === file.id &&
                (startParseMutation.isPending || stopParseMutation.isPending) ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <>
                    {file.run_status === RunStatus.Running ? (
                      <Square className="h-4 w-4" />
                    ) : file.run_status === RunStatus.Fail ? (
                      <RefreshCw className="text-destructive h-4 w-4" />
                    ) : file.run_status === RunStatus.Cancelling ? (
                      <Square className="h-4 w-4" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                  </>
                )}
              </IconButton>

              {/* 元信息设置 */}
              <IconButton
                variant="ghost"
                tooltip={
                  file.run_status !== RunStatus.Done ? "未解析成功不能进行元信息设置" : "元信息设置"
                }
                onClick={() => {
                  router.push(`/datasets/file/${datasetId}/${file.id}`);
                }}
                disabled={file.run_status !== RunStatus.Done}
              >
                <Settings className="h-4 w-4" />
              </IconButton>

              {/* 重置解析按钮 */}
              <IconButton
                variant="ghost"
                tooltip={
                  [RunStatus.Running, RunStatus.Cancelling, RunStatus.Unstart].includes(
                    file.run_status
                  )
                    ? "当前状态不能重置解析"
                    : "重置解析"
                }
                onClick={() => {
                  setFileToReset({ id: file.id, name: file.name });
                }}
                disabled={[RunStatus.Running, RunStatus.Cancelling, RunStatus.Unstart].includes(
                  file.run_status
                )}
              >
                <RefreshCw className="h-4 w-4" />
              </IconButton>

              {/* 删除按钮 */}
              <IconButton
                variant="ghost"
                tooltip={!isRowOwner ? "您没有权限删除" : "删除文件"}
                onClick={() => handleDeleteFile(file.id, file.name)}
                disabled={!isRowOwner}
              >
                <Trash2 className="text-destructive h-4 w-4" />
              </IconButton>
            </div>
          );
        },
      },
      "right",
      200
    ),
  ];

  return (
    <CustomContainer
      title={fileset.name || "文件数据集详情"}
      breadcrumbs={breadcrumbs}
      action={
        <div className="flex items-center gap-2">
          {/* 批量操作按钮 */}

          <div className="mr-4 flex items-center gap-2">
            <span className="text-muted-foreground text-sm">
              已选择 {selection.selectedRows.length} 项
            </span>
            <Button
              size="sm"
              variant="outline"
              disabled={
                (startParseMutation.isPending && !operatingFileId) ||
                selection.selectedRows.length <= 0
              }
              onClick={() => handleBatchParse(true)}
            >
              {startParseMutation.isPending && !operatingFileId ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                "批量解析"
              )}
            </Button>
            {/* <Button
              size="sm"
              variant="outline"
              disabled={
                (stopParseMutation.isPending && !operatingFileId) ||
                selection.selectedRows.length <= 0
              }
              onClick={() => handleBatchParse(false)}
            >
              {stopParseMutation.isPending && !operatingFileId ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                "批量停止"
              )}
            </Button> */}
            <Button
              size="sm"
              variant="destructive"
              disabled={
                (deleteDocumentsMutation.isPending && !operatingFileId) ||
                selection.selectedRows.length <= 0
              }
              onClick={handleBatchDelete}
            >
              批量删除
            </Button>
          </div>

          <Button size="sm" onClick={handleAddNewDocument}>
            <PlusIcon className="h-4 w-4" />
            添加文件
          </Button>
        </div>
      }
    >
      <div className="space-y-6">
        {/* 知识库信息 */}
        <div className="text-muted-foreground bg-muted/30 mb-4 flex items-center gap-6 rounded-lg p-2 text-sm">
          <div className="flex items-center gap-2">
            <span className="font-medium">创建时间:</span>
            {fileset.create_time ? formatDateTime(new Date(fileset.create_time)) : ""}
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium">文档数量:</span>
            {data?.total}
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium">创建人:</span>
            {fileset?.creator?.name}
          </div>
        </div>
        <div className="no-scrollbar w-full">
          <PaginatedTable
            columns={columns}
            data={data}
            isLoading={isLoading}
            currentPage={pageNumber}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            showTotal={true}
            showPageSizeSelector={true}
            enableSelection={true}
            selectedRowIds={selection.selectedRowIds}
            onSelectionChange={selection.handleSelectionChange}
            onCurrentPageSelectionChange={selection.handleCurrentPageSelectionChange}
            className="no-scrollbar w-full"
            tableContainerClassName="overflow-x-auto border max-w-full no-scrollbar"
            tableClassName="min-w-[1200px]"
          />
        </div>
      </div>

      {/* 切片方法设置模态框 */}
      {/* {isChunkMethodModalOpen && selectedFileForChunkMethod && (
        <ChunkMethodModal
          open={isChunkMethodModalOpen}
          onClose={() => setIsChunkMethodModalOpen(false)}
          onSubmit={handleSaveChunkMethod}
          file={selectedFileForChunkMethod}
        />
      )} */}
      {/* 确认重置解析对话框 */}
      <ConfirmDialog
        open={!!fileToReset}
        onOpenChange={(open) => !open && setFileToReset(null)}
        onConfirm={confirmResetParse}
        title={t("resetParse")}
        description={`重置解析后，现有的的元数据配置也会被重置，确定要重置文件 "${fileToReset?.name}" 的解析吗？此操作无法撤销。`}
      />

      {/* 删除确认对话框 */}
      <ConfirmDialog
        open={!!fileToDelete}
        onOpenChange={(open) => !open && setFileToDelete(null)}
        onConfirm={confirmDeleteFile}
        title={t("deleteFile")}
        description={`确定要删除文件 "${fileToDelete?.name}" 吗？此操作无法撤销。`}
      />

      {/* 批量删除确认对话框 */}
      <ConfirmDialog
        open={!!batchDeleteConfirm}
        onOpenChange={(open) => !open && setBatchDeleteConfirm(null)}
        onConfirm={confirmBatchDelete}
        title={t("batchDelete")}
        description={`确定要批量删除 ${batchDeleteConfirm?.count} 个文件吗？此操作无法撤销。`}
      />

      {/* 文件选择抽屉 */}
      {isFileSelectionDrawerOpen && (
        <FileSelectionDrawer
          hasReferenceNum={data?.total || 0}
          open={isFileSelectionDrawerOpen}
          onClose={() => setIsFileSelectionDrawerOpen(false)}
          onConfirm={handleFileSelectionConfirm}
          datasetId={datasetId}
          filesetName={fileset?.name || "文件数据集"}
        />
      )}
    </CustomContainer>
  );
}

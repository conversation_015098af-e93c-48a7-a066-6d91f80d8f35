"use client";

import { formatFileSize } from "@/app/(basic)/files/utils";
import { getFileIcon } from "@/lib/file-utils";
import { formatDateTime } from "@/lib/utils";
import { useInfiniteFiles, type FileItem } from "@/service/file-service";
import { Badge } from "@ragtop-web/ui/components/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@ragtop-web/ui/components/breadcrumb";
import { Button } from "@ragtop-web/ui/components/button";
import { Checkbox } from "@ragtop-web/ui/components/checkbox";
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer";
import { SelectionList, type SelectionItem } from "@ragtop-web/ui/components/selection-list";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { Check, ChevronRight, FileText, FolderIcon } from "lucide-react";
import { useMemo, useState } from "react";

const MAX_REFERENCE_NUM = 100;

interface FileSelectionDrawerProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (selectedFileIds: string[]) => void;
  datasetId: string;
  filesetName: string;
  hasReferenceNum: number;
}

interface BreadcrumbItem {
  id: string | null;
  name: string;
  path: string;
}

/**
 * 文件选择抽屉组件
 * 用于从文件列表中选择文件并添加到文件数据集
 */
export function FileSelectionDrawer({
  open,
  onClose,
  onConfirm,
  datasetId,
  filesetName,
  hasReferenceNum,
}: FileSelectionDrawerProps) {
  const { toast } = useToast();
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);
  const [breadcrumbPath, setBreadcrumbPath] = useState<BreadcrumbItem[]>([
    { id: null, name: "根目录", path: "/" },
  ]);

  // 使用无限滚动获取文件列表
  const {
    data: fileList,
    isLoading,
    hasMore,
    fetchNextPage,
    isFetchingNextPage,
    total,
  } = useInfiniteFiles(undefined, "EXCLUDE", datasetId, currentFolderId, 20, open);
  // 过滤文件（基于搜索词）
  const filteredFiles = useMemo(() => {
    return fileList.filter((file: FileItem) =>
      file.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [fileList, searchTerm]);

  // 转换数据为 SelectionItem 格式
  const selectionItems: SelectionItem[] = filteredFiles.map((file: FileItem) => ({
    id: file.id,
    label: file.name,
    disabled: false,
  }));

  // 处理选择变化
  const handleSelectionChange = (selectedIds: string | string[]) => {
    if (Array.isArray(selectedIds)) {
      setSelectedFileIds(selectedIds);
    }
  };

  // 处理文件夹点击（下钻）
  const handleFolderClick = (folderId: string, folderName: string) => {
    const newBreadcrumbItem: BreadcrumbItem = {
      id: folderId,
      name: folderName,
      path: `/${folderId}`,
    };

    setBreadcrumbPath([...breadcrumbPath, newBreadcrumbItem]);
    setCurrentFolderId(folderId);
    setSelectedFileIds([]); // 清空选择
  };

  // 处理面包屑导航
  const handleBreadcrumbClick = (index: number) => {
    const newBreadcrumbPath = breadcrumbPath.slice(0, index + 1);
    setBreadcrumbPath(newBreadcrumbPath);

    const targetItem = newBreadcrumbPath[newBreadcrumbPath.length - 1];
    if (targetItem) {
      setCurrentFolderId(targetItem.id);
    }
    setSelectedFileIds([]); // 清空选择
  };

  // 处理确认选择
  const handleConfirm = () => {
    const referenceNum = hasReferenceNum + selectedFileIds.length;
    if (referenceNum > MAX_REFERENCE_NUM) {
      toast({
        title: `文件数据集关联的文件数量最大${MAX_REFERENCE_NUM}个`,
        variant: "destructive",
      });
      return;
    }

    const delta = MAX_REFERENCE_NUM - hasReferenceNum;
    onConfirm(selectedFileIds.slice(0, delta));
    // 重置状态
    setSelectedFileIds([]);
    setSearchTerm("");
    setCurrentFolderId(null);
    setBreadcrumbPath([{ id: null, name: "根目录", path: "/" }]);
  };

  // 处理取消
  const handleCancel = () => {
    onClose();
    // 重置状态
    setSelectedFileIds([]);
    setSearchTerm("");
    setCurrentFolderId(null);
    setBreadcrumbPath([{ id: null, name: "根目录", path: "/" }]);
  };

  // 自定义渲染文件项
  const renderFileItem = (
    item: SelectionItem,
    isSelected: boolean,
    isDisabled: boolean,
    onSelect: (itemId: string) => void
  ) => {
    const file = filteredFiles.find((f: FileItem) => f.id === item.id);
    if (!file) return null;

    const isFolder = file.isFolder || file.type === "folder";

    return (
      <div
        key={item.id}
        className={`flex w-full items-center rounded-md border px-3 py-2 transition-colors ${
          isSelected ? "bg-primary/10 border-primary" : "hover:bg-muted border-border"
        } ${isDisabled || (isFolder && file.unlink_file_num === 0) ? "cursor-not-allowed opacity-50" : "cursor-pointer"}`}
        onClick={() =>
          !(isDisabled || (isFolder && file.unlink_file_num === 0)) && onSelect(item.id)
        }
      >
        <Checkbox
          checked={isSelected}
          disabled={isDisabled || (isFolder && file.unlink_file_num === 0)}
          className="pointer-events-none mr-3"
        />
        <div className="min-w-0 flex-1">
          <div className="mb-1 flex items-center gap-2">
            {getFileIcon(file.type)}
            <span className="truncate font-medium">{file.name}</span>

            {/* 文件夹点击下钻按钮 */}
            {isFolder && (
              <Button
                disabled={file.unlink_file_num === 0}
                variant="ghost"
                size="sm"
                className="ml-auto h-6 w-6 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  handleFolderClick(file.id, file.name);
                }}
              >
                <ChevronRight className="h-3 w-3" />
              </Button>
            )}
          </div>
          <div className="text-muted-foreground text-xs">
            {isFolder
              ? `未被链接数: ${file.unlink_file_num} | 创建时间: ${formatDateTime(new Date(file.create_time))}`
              : `大小: ${formatFileSize(file.size)} | 创建时间: ${formatDateTime(new Date(file.create_time))}`}
          </div>
        </div>
      </div>
    );
  };

  return (
    <CustomDrawer
      open={open}
      onClose={handleCancel}
      className="sm:max-w-xl"
      title={`选择文件 - ${filesetName}`}
      footer={
        <div className="flex w-full items-center justify-between">
          <div className="flex gap-2">
            <Button
              onClick={handleConfirm}
              disabled={
                selectedFileIds.length === 0 ||
                hasReferenceNum + selectedFileIds.length > MAX_REFERENCE_NUM
              }
            >
              <Check className="h-4 w-4" />
              添加到文件数据集
            </Button>
          </div>
          <div className="flex flex-col space-y-1 text-sm">
            <span className="text-xs font-medium">已选择 {selectedFileIds.length} 个文件</span>
            <span className="text-muted-foreground text-xs font-medium">
              最多添加{MAX_REFERENCE_NUM}个文件({hasReferenceNum + selectedFileIds.length}/
              {MAX_REFERENCE_NUM})
            </span>
          </div>
        </div>
      }
    >
      <div className="space-y-4">
        {/* 面包屑导航 */}
        <Breadcrumb>
          <BreadcrumbList>
            {breadcrumbPath.map((item, index) => (
              <div key={item.path} className="flex items-center">
                {index > 0 && <BreadcrumbSeparator />}
                <BreadcrumbItem>
                  {index === breadcrumbPath.length - 1 ? (
                    <BreadcrumbPage className="flex items-center">
                      <FolderIcon className="mr-1 h-4 w-4" />
                      {item.name}
                    </BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink
                      className="flex cursor-pointer items-center"
                      onClick={() => handleBreadcrumbClick(index)}
                    >
                      <FolderIcon className="mr-1 h-4 w-4" />
                      {item.name}
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
              </div>
            ))}
          </BreadcrumbList>
        </Breadcrumb>

        {/* 文件统计 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="text-primary h-5 w-5" />
            <span className="text-sm font-medium">文件列表</span>
          </div>
          <Badge variant="outline">{total} 个文件</Badge>
        </div>

        <SelectionList
          items={selectionItems}
          isLoading={isLoading}
          isLoadingMore={isFetchingNextPage}
          hasMore={hasMore}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          selectionMode="multiple"
          selectedIds={selectedFileIds}
          onSelectionChange={handleSelectionChange}
          searchPlaceholder="搜索文件..."
          emptyText="未找到匹配的文件"
          loadingText="加载中..."
          endText="已显示所有文件"
          height="400px"
          renderItem={renderFileItem}
          onScroll={(e) => {
            const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
            // 当滚动到距离底部200px时触发加载更多
            if (scrollHeight - scrollTop - clientHeight < 200 && hasMore && !isFetchingNextPage) {
              fetchNextPage();
            }
          }}
        />
      </div>
    </CustomDrawer>
  );
}

"use client";
import { useIsTeamAdmin } from "@/lib/user-role";
import { siteConfigAtom } from "@/store/user-store";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { useAtomValue } from "jotai";
import { useTranslations } from "next-intl";

/**
 * 首页组件
 *
 * 显示系统概览和快速访问链接
 */
export default function HomePage() {
  const isAdmin = useIsTeamAdmin();
  const siteConfig = useAtomValue(siteConfigAtom);
  const t = useTranslations("home");

  return (
    <CustomContainer title={t("welcomeTitle", { siteName: siteConfig?.name })}>
      <div className="space-y-8">
        {/* 欢迎信息 */}
        <div className="bg-muted/30 rounded-lg p-6">
          <h2 className="mb-2 text-2xl font-bold">{t("welcomeBack")}</h2>
          <p className="text-muted-foreground">
            {siteConfig?.name}
            {t("description")}
            {isAdmin ? t("adminDescription") : t("userDescription")}
          </p>
        </div>
      </div>
    </CustomContainer>
  );
}

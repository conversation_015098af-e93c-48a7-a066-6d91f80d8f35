"use client";

import { getFileIcon } from "@/lib/file-utils";
import { formatDateTime } from "@/lib/utils";
import { FileItem, useDeleteFile, useFiles, useRenameFile } from "@/service";
import { Badge } from "@ragtop-web/ui/components/badge";
import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";
import { type ColumnDef } from "@ragtop-web/ui/components/data-table";
import { PaginatedTable } from "@ragtop-web/ui/components/paginated-table";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { usePagination } from "@ragtop-web/ui/hooks/use-pagination";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { formatFileSize, truncateFileName } from "../utils";
import { FileActionMenu } from "./file-action-menu";
import { RenameFileDialog } from "./rename-file-dialog";

interface FileListProps {
  onFolderClick: (folderId: string, folderName: string) => void;
  currentFolderId?: string | null;
  uploadSuccessTrigger?: number;
}

export function FileList({ onFolderClick, currentFolderId, uploadSuccessTrigger }: FileListProps) {
  const { toast } = useToast();
  const t = useTranslations("files");
  const deleteFile = useDeleteFile();
  const renameFile = useRenameFile();

  const [fileToDelete, setFileToDelete] = useState<FileItem | null>(null);
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);

  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);
  const { pageNumber, pageSize, handlePageChange, handlePageSizeChange, resetToFirstPage } =
    usePagination({
      initialPageSize: 10,
    });

  const {
    data: filesData,
    isLoading,
    error,
  } = useFiles(pageNumber, pageSize, undefined, undefined, undefined, currentFolderId);

  // 监听文件列表数据变化，当数据刷新时重置到第一页
  useEffect(() => {
    if (uploadSuccessTrigger && uploadSuccessTrigger > 0) {
      // 当上传成功触发时，重置分页状态到第一页
      resetToFirstPage();
    }
  }, [uploadSuccessTrigger, resetToFirstPage]);

  // 处理重命名
  const handleRenameFile = (file: FileItem) => {
    setSelectedFile(file);
    setIsRenameDialogOpen(true);
  };

  // 处理确认重命名
  const handleConfirmRename = (newName: string) => {
    if (!selectedFile) return;

    renameFile.mutate(
      {
        file_id: selectedFile.id,
        name: newName,
      },
      {
        onSuccess: () => {
          toast({
            title: t("messages.renameFolderSuccess"),
            description: t("messages.renameFolderSuccessDesc", { newName }),
          });
          setIsRenameDialogOpen(false);
        },
        onError: (error) => {
          toast({
            title: t("messages.renameFolderFailed"),
            description: t("messages.renameFolderFailedDesc"),
            variant: "destructive",
          });
        },
      }
    );
  };

  // 处理文件删除
  const handleDeleteFile = (fileId: string) => {
    deleteFile.mutate(
      { file_id: fileId },
      {
        onSuccess: () => {
          toast({
            title: t("messages.deleteSuccess"),
            description: t("messages.deleteSuccessDesc"),
          });
          // 删除成功后，重置分页状态到第一页
          resetToFirstPage();
        },
        onError: (error) => {
          toast({
            title: t("messages.deleteFailed"),
            description: t("messages.deleteFailedDesc"),
            variant: "destructive",
          });
        },
      }
    );
  };

  // 处理文件夹点击
  const handleFolderClick = (file: FileItem) => {
    if (file.type === "folder") {
      resetToFirstPage();
      onFolderClick(file.id, file.name);
    }
  };

  // 处理删除文件
  const handleDelete = (file: FileItem) => {
    setFileToDelete(file);
    // if (!file.isFolder && file.references > 0) {
    //   // 如果文件被引用，显示确认对话框
    //   setFileToDelete(file)
    // } else {
    //   // 如果文件未被引用或是文件夹，直接删除
    //   onDeleteFile(file.id)
    // }
  };

  // 确认删除
  const confirmDelete = () => {
    if (fileToDelete) {
      handleDeleteFile(fileToDelete.id);
      setFileToDelete(null);
    }
  };

  // 定义表格列
  const columns: ColumnDef<FileItem>[] = [
    {
      accessorKey: "name",
      header: t("table.name"),
      size: 400,
      cell: ({ row }) => {
        const file = row.original;
        return (
          <div
            className={`flex items-center gap-2 truncate font-medium ${file.type === "folder" ? "hover:text-primary cursor-pointer" : ""}`}
            onClick={() => file.type === "folder" && handleFolderClick(file)}
          >
            {getFileIcon(file.type)}
            {truncateFileName(file.name, 40)}
          </div>
        );
      },
    },
    {
      accessorKey: "type",
      header: t("table.type"),
      cell: ({ row }) => <Badge variant="outline">{row.original.type}</Badge>,
    },
    {
      accessorKey: "size",
      header: t("table.size"),
      cell: ({ row }) => formatFileSize(row.original.size),
    },
    {
      accessorKey: "link_num",
      header: t("table.linkedCount"),
    },
    {
      accessorKey: "create_time",
      header: t("table.createTime"),
      size: 200,
      cell: ({ row }) => formatDateTime(row.original.create_time || ""),
    },
    {
      accessorKey: "owner_name",
      header: t("table.creator"),
      cell: ({ row }) => row.original.creator?.name,
    },
    {
      accessorKey: "update_time",
      header: t("table.updateTime"),
      cell: ({ row }) => formatDateTime(row?.original?.update_time || ""),
    },
    {
      accessorKey: "update_name",
      header: t("table.updater"),
      cell: ({ row }) => row.original.updator?.name,
    },
    {
      id: "actions",
      header: t("table.actions"),
      cell: ({ row }) => {
        const file = row.original;
        return <FileActionMenu file={file} onRename={handleRenameFile} onDelete={handleDelete} />;
      },
    },
  ];

  return (
    <>
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div>
          <span className="ml-2">加载中...</span>
        </div>
      ) : error ? (
        <div className="text-destructive py-12 text-center">加载文件列表失败，请刷新页面重试</div>
      ) : (
        <>
          <PaginatedTable
            columns={columns}
            data={filesData}
            isLoading={isLoading}
            currentPage={pageNumber}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            showTotal={true}
            showPageSizeSelector={true}
          />
          <ConfirmDialog
            open={!!fileToDelete}
            onOpenChange={(open) => !open && setFileToDelete(null)}
            title={t("dialog.deleteTitle")}
            description={
              fileToDelete?.type === "folder"
                ? "确定要删除这个文件夹吗？文件夹内的文件可能被知识库或文件数据集链接，删除后，该文件夹下的所有文件和子文件夹将被删除，链接关系也会被取消，确定要删除吗？"
                : fileToDelete?.link_num
                  ? ` 该文件正在被 ${fileToDelete?.link_num} 个知识库或文件数据集链接。
        删除后链接关系也会被取消，确定要删除吗？`
                  : "确定要删除这个文件吗？"
            }
            onConfirm={confirmDelete}
            onCancel={() => setFileToDelete(null)}
            confirmText={t("dialog.deleteConfirm")}
            cancelText={t("dialog.deleteCancel")}
            loading={false}
            disabled={!fileToDelete}
          />
          {/* 重命名对话框 */}
          {selectedFile && (
            <RenameFileDialog
              open={isRenameDialogOpen}
              onClose={() => setIsRenameDialogOpen(false)}
              onRename={handleConfirmRename}
              file={selectedFile}
            />
          )}
        </>
      )}
    </>
  );
}

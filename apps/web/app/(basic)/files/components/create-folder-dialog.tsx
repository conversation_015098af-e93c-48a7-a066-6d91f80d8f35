"use client";

import { But<PERSON> } from "@ragtop-web/ui/components/button";
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import { Input } from "@ragtop-web/ui/components/input";
import { Label } from "@ragtop-web/ui/components/label";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";

interface CreateFolderDialogProps {
  open: boolean;
  onClose: () => void;
  onCreateFolder: (folderName: string) => void;
  currentPath: string;
}

interface FormValues {
  folderName: string;
}

/**
 * 创建文件夹对话框组件
 */
export function CreateFolderDialog({
  open,
  onClose,
  onCreateFolder,
  currentPath,
}: CreateFolderDialogProps) {
  const t = useTranslations("forms.placeholders");
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormValues>({
    defaultValues: {
      folderName: "",
    },
  });

  // 处理表单提交
  const onSubmit = (data: FormValues) => {
    onCreateFolder(data.folderName);
    reset();
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>新建文件夹</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="folderName">文件夹名称</Label>
              <Input
                id="folderName"
                placeholder={t("folderName")}
                {...register("folderName", {
                  required: "文件夹名称不能为空",
                  pattern: {
                    value: /^[^\\/:*?"<>|]+$/,
                    message: '文件夹名称不能包含特殊字符 \\ / : * ? " < > |',
                  },
                })}
              />
              {errors.folderName && (
                <p className="text-destructive text-sm">{errors.folderName.message}</p>
              )}
            </div>
            <div className="text-muted-foreground text-sm">
              当前位置: {currentPath === "/" ? "root" : currentPath}
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button type="submit">创建</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

"use client";

import { But<PERSON> } from "@ragtop-web/ui/components/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import { Input } from "@ragtop-web/ui/components/input";
import { Label } from "@ragtop-web/ui/components/label";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
// 文件项类型
interface FileItem {
  id: string;
  name: string;
  type: string;
  size: number;
  link_num: number;
  create_time: string;
  isFolder: boolean;
  parentId: string | null;
  path: string;
}

interface RenameFileDialogProps {
  open: boolean;
  onClose: () => void;
  onRename: (newName: string) => void;
  file: FileItem | null;
}

interface FormValues {
  newName: string;
}

/**
 * 重命名文件/文件夹对话框组件
 */
export function RenameFileDialog({ open, onClose, onRename, file }: RenameFileDialogProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<FormValues>({
    defaultValues: {
      newName: "",
    },
  });

  // 当文件变更时，设置默认值
  useEffect(() => {
    if (file) {
      setValue("newName", file.name);
    }
  }, [file, setValue]);

  // 处理表单提交
  const onSubmit = (data: FormValues) => {
    onRename(data.newName);
    reset();
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>重命名{file?.isFolder ? "文件夹" : "文件"}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="newName">新名称</Label>
              <Input
                id="newName"
                placeholder={`请输入新的${file?.isFolder ? "文件夹" : "文件"}名称`}
                {...register("newName", {
                  required: "名称不能为空",
                  pattern: {
                    value: /^[^\\/:*?"<>|]+$/,
                    message: '名称不能包含特殊字符 \\ / : * ? " < > |',
                  },
                })}
              />
              {errors.newName && (
                <p className="text-destructive text-sm">{errors.newName.message}</p>
              )}
            </div>
            <div className="text-muted-foreground text-sm">当前名称: {file?.name}</div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button type="submit">确认</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

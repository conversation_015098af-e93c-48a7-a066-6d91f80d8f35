/**
 * 文件上传Hook
 *
 * 提供文件上传的统一逻辑，支持多文件上传、进度跟踪、错误处理和重试功能
 */

import { useConfig } from "@/components/config-provider-client";
import { useUploadFile } from "@/service";
import { allExtensionAtom } from "@/store/kbase-store";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { useAtomValue } from "jotai";
import { useCallback, useState } from "react";
import { MAX_FILE_COUNT, validateFileSize, validateFileType } from "../utils";

export interface FileUploadItem {
  file: File;
  id: string;
  status: "pending" | "uploading" | "success" | "error";
  progress: number;
  error?: string;
  abortController?: AbortController;
}

export interface UseFileUploadOptions {
  onSuccess?: (files: FileUploadItem[]) => void;
  onError?: (error: string) => void;
  onProgress?: (fileId: string, progress: number) => void;
  maxFiles?: number;
  autoUpload?: boolean;
  parentId?: string | null;
}

export function useFileUpload(options: UseFileUploadOptions = {}) {
  const {
    onSuccess,
    onError,
    onProgress,
    maxFiles = MAX_FILE_COUNT,
    autoUpload = false,
    parentId,
  } = options;
  const { toast } = useToast();
  const fileSizeLimit = useConfig().FILE_SIZE_LIMIT;
  const tableFileSizeLimit = useConfig().TABLE_FILE_SIZE_LIMIT;
  const [files, setFiles] = useState<FileUploadItem[]>([]);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const uploadFileMutation = useUploadFile();
  const supportedFileTypes = useAtomValue(allExtensionAtom);

  // 生成唯一ID
  const generateId = useCallback(() => {
    return Math.random().toString(36).substring(2, 11);
  }, []);

  // 验证文件
  const validateFiles = useCallback(
    (fileList: File[]) => {
      const errors: string[] = [];
      // 验证文件数量
      if (fileList.length > maxFiles) {
        errors.push(`最多只能选择 ${maxFiles} 个文件`);
        return { validFiles: [], errors };
      }

      const validFiles: FileUploadItem[] = [];

      fileList.forEach((file) => {
        // 验证文件类型
        if (!validateFileType(file, supportedFileTypes)) {
          errors.push(`文件 "${file.name}" 类型不支持`);
          return;
        }

        // 验证文件大小
        if (!validateFileSize(file, fileSizeLimit, tableFileSizeLimit)) {
          errors.push(`文件 "${file.name}" 大小超过限制`);
          return;
        }

        validFiles.push({
          file,
          id: generateId(),
          status: "pending",
          progress: 0,
          abortController: new AbortController(),
        });
      });

      return { validFiles, errors };
    },
    [maxFiles, generateId]
  );

  // 添加文件
  const addFiles = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const fileList = Array.from(e.target.files || []);

      const { validFiles, errors } = validateFiles(fileList);

      setValidationErrors(errors);
      setFiles((prev) => [...prev, ...validFiles]);

      // 如果启用自动上传且没有错误，则开始上传
      if (autoUpload && errors.length === 0 && validFiles.length > 0) {
        uploadFiles(validFiles);
      }
      // 清空文件，防止重名文件取消后不能重新选择
      e.target.value = "";
      return validFiles;
    },
    [validateFiles, autoUpload]
  );

  // 上传单个文件
  const uploadSingleFile = useCallback(
    async (fileItem: FileUploadItem): Promise<void> => {
      return new Promise(async (resolve, reject) => {
        let progressInterval: NodeJS.Timeout | null = null;

        // 更新文件状态为上传中
        setFiles((prev) =>
          prev.map((f) => (f.id === fileItem.id ? { ...f, status: "uploading", progress: 0 } : f))
        );

        // 创建FormData
        const formData = new FormData();
        formData.append("file", fileItem.file);

        const cleanup = () => {
          if (progressInterval) {
            clearInterval(progressInterval);
            progressInterval = null;
          }
        };
        // 使用真实的上传API
        try {
          await uploadFileMutation.mutateAsync(
            {
              formData,
              abortController: fileItem.abortController,
              parentId,
            }
            // {
            //   onSuccess: () => {
            //     console.log('请求结果')

            //   },
            //   onError: (error: any) => {
            //     cleanup();
            //     const errorMessage = error?.message || "上传失败，请重试";
            //     setFiles((prev) =>
            //       prev.map((f) =>
            //         f.id === fileItem.id
            //           ? {
            //               ...f,
            //               status: "error",
            //               progress: 0,
            //               error: errorMessage,
            //             }
            //           : f
            //       )
            //     );
            //     onError?.(errorMessage);
            //     reject(error);
            //   },
            // }
          );
          cleanup();
          setFiles((prev) =>
            prev.map((f) => (f.id === fileItem.id ? { ...f, status: "success", progress: 100 } : f))
          );
          onProgress?.(fileItem.id, 100);
          resolve();
        } catch (error: any) {
          cleanup();
          const errorMessage = error?.message || "上传失败，请重试";
          setFiles((prev) =>
            prev.map((f) =>
              f.id === fileItem.id
                ? {
                    ...f,
                    status: "error",
                    progress: 0,
                    error: errorMessage,
                  }
                : f
            )
          );
          onError?.(errorMessage);
          reject(error);
        }

        // 模拟进度更新（实际项目中应该从API获取真实进度）
        progressInterval = setInterval(() => {
          setFiles((prev) => {
            const targetFile = prev.find((f) => f.id === fileItem.id);
            if (!targetFile || targetFile.status !== "uploading") {
              cleanup();
              return prev;
            }

            const newProgress = Math.min(targetFile.progress + Math.random() * 15, 95);
            onProgress?.(fileItem.id, newProgress);

            return prev.map((f) => (f.id === fileItem.id ? { ...f, progress: newProgress } : f));
          });
        }, 200);
      });
    },
    [uploadFileMutation, onProgress, onError]
  );

  // 批量上传文件
  const uploadFiles = useCallback(
    async (filesToUpload?: FileUploadItem[]) => {
      const finalFiles = filesToUpload || files;
      const targetFiles = finalFiles.filter((f) => f.status === "pending" || f.status === "error");

      if (targetFiles.length === 0) return;

      try {
        // 创建一个Map来跟踪每个文件的上传状态
        const uploadTracker = new Map<string, { completed: boolean; error?: string }>();
        targetFiles.forEach((file) => uploadTracker.set(file.id, { completed: false }));

        // 设置并发上传数量
        const MAX_CONCURRENT_UPLOADS = 5;
        const uploadQueue = [...targetFiles];
        let activeUploads: Promise<void>[] = [];

        const processUpload = async (file: FileUploadItem): Promise<void> => {
          try {
            await uploadSingleFile(file);
            uploadTracker.set(file.id, { completed: true });
          } catch (error: any) {
            uploadTracker.set(file.id, {
              completed: true,
              error: error.message,
            });
            throw error; // 继续抛出错误以便上层处理
          }
        };

        while (uploadQueue.length > 0 || activeUploads.length > 0) {
          // 填充活跃上传队列直到达到最大并发数
          while (uploadQueue.length > 0 && activeUploads.length < MAX_CONCURRENT_UPLOADS) {
            const file = uploadQueue.shift()!;
            activeUploads.push(processUpload(file));
          }

          if (activeUploads.length > 0) {
            // 等待所有当前活跃的上传完成
            const results = await Promise.allSettled(activeUploads);

            // 处理每个上传的结果
            results.forEach((result) => {
              if (result.status === "rejected") {
                console.error("文件上传失败:", result.reason);
              }
            });

            // 清空活跃上传数组，准备下一批
            activeUploads = [];
          }
        }

        // 检查上传结果
        const errors: string[] = [];
        uploadTracker.forEach((status, fileId) => {
          if (status.error) {
            errors.push(`文件 ${fileId} 上传失败: ${status.error}`);
          }
        });

        if (errors.length > 0) {
          onError?.(errors.join("\n"));
        }

        const successFiles = files.filter((f) => f.status === "success");
        if (successFiles.length > 0) {
          onSuccess?.(successFiles);
        }
      } catch (error) {
        toast({
          title: "上传失败，请稍后重试",
          variant: "destructive",
        });
        console.error("批量上传失败:", error);
        onError?.("批量上传失败");
      }
    },
    [files, uploadSingleFile, onSuccess, onError]
  );

  // 重试上传失败的文件
  const retryFile = useCallback(
    (fileId: string) => {
      const fileItem = files.find((f) => f.id === fileId);
      if (fileItem && fileItem.status === "error") {
        uploadSingleFile(fileItem);
      }
    },
    [files, uploadSingleFile]
  );

  // 移除文件
  const removeFile = useCallback((fileId: string) => {
    setFiles((prev) => {
      const fileItem = prev.find((f) => f.id === fileId);
      if (fileItem && fileItem.status === "uploading") {
        fileItem.abortController?.abort("用户取消");
      }
      return prev.filter((f) => f.id !== fileId);
    });
    // setFiles((prev) => prev.filter((f) => f.id !== fileId));
  }, []);

  // 清空所有文件
  const clearFiles = useCallback(() => {
    setFiles([]);
    setValidationErrors([]);
  }, []);

  // 获取统计信息
  const stats = {
    total: files.length,
    pending: files.filter((f) => f.status === "pending").length,
    uploading: files.filter((f) => f.status === "uploading").length,
    success: files.filter((f) => f.status === "success").length,
    error: files.filter((f) => f.status === "error").length,
    isUploading: files.some((f) => f.status === "uploading"),
    hasErrors: files.some((f) => f.status === "error"),
    hasSuccess: files.some((f) => f.status === "success"),
  };

  return {
    files,
    validationErrors,
    stats,
    addFiles,
    uploadFiles,
    retryFile,
    removeFile,
    clearFiles,
    isUploading: uploadFileMutation.isPending,
  };
}

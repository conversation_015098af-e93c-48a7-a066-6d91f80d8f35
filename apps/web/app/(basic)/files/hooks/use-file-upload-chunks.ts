import { useConfig } from "@/components/config-provider-client";
import {
  useCompleteMultipartUpload,
  useInitMultipartUpload,
  type ETagPart,
} from "@/service/file-service";
import { allExtensionAtom } from "@/store/kbase-store";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { useAtomValue } from "jotai";
import { useCallback, useState } from "react";
import { MAX_FILE_COUNT, validateFileSize, validateFileType } from "../utils";

// Types
export interface FileUploadItem {
  file: File;
  id: string;
  status: "pending" | "uploading" | "success" | "error";
  progress: number;
  error?: string;
  abortController?: AbortController;
}

export interface UseFileUploadOptions {
  onSuccess?: (files: FileUploadItem[]) => void;
  onError?: (error: string) => void;
  onProgress?: (fileId: string, progress: number) => void;
  maxFiles?: number;
  autoUpload?: boolean;
  parentId?: string | null;
}

interface ChunkInfo {
  fileChunk: Blob;
  fileType: string;
  preSignedUrl: string;
}

export function useFileUploadChunks(options: UseFileUploadOptions = {}) {
  const {
    onSuccess,
    onError,
    onProgress,
    maxFiles = MAX_FILE_COUNT,
    autoUpload = false,
    parentId,
  } = options;
  const { toast } = useToast();
  const fileSizeLimit = useConfig().FILE_SIZE_LIMIT;
  const tableFileSizeLimit = useConfig().TABLE_FILE_SIZE_LIMIT;
  const [files, setFiles] = useState<FileUploadItem[]>([]);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const supportedFileTypes = useAtomValue(allExtensionAtom);
  const initMultipartUploadMutation = useInitMultipartUpload();
  const completeMultipartUploadMutation = useCompleteMultipartUpload();

  // Generate unique ID
  const generateId = useCallback(() => {
    return Math.random().toString(36).substring(2, 11);
  }, []);

  // Upload chunk with retry
  const uploadChunk = async (
    chunkInfo: ChunkInfo,
    controller: AbortController,
    attempt = 0,
    maxRetries = 3
  ): Promise<string> => {
    const { fileChunk, fileType, preSignedUrl } = chunkInfo;

    try {
      const response = await fetch(preSignedUrl, {
        method: "PUT",
        body: fileChunk,
        headers: {
          "Content-Type": fileType,
        },
        signal: controller?.signal,
      });

      if (!response.ok) {
        throw new Error(`Upload failed with status ${response.status}`);
      }

      const etag = response.headers.get("etag");
      if (!etag) {
        throw new Error("No ETag received from server");
      }

      return etag;
    } catch (error) {
      if (attempt < maxRetries) {
        return await uploadChunk(chunkInfo, controller, attempt + 1, maxRetries);
      }
      throw error;
    }
  };

  // Upload single file
  const uploadSingleFile = useCallback(
    async (fileItem: FileUploadItem): Promise<void> => {
      return new Promise(async (resolve, reject) => {
        try {
          // Set status to uploading
          setFiles((prev) =>
            prev.map((f) => (f.id === fileItem.id ? { ...f, status: "uploading" } : f))
          );

          // Initialize multipart upload
          const initResponse = await initMultipartUploadMutation.mutateAsync({
            file: fileItem.file,
            parentId,
          });

          // Extract data from the response - handle both direct response and wrapped response
          const responseData = (initResponse as any).data?.[0] || initResponse;

          // Handle both possible response formats
          const uploadId = responseData.upload_id;
          const partItems = responseData.part_items || [];

          if (!uploadId || !partItems || partItems.length === 0) {
            throw new Error("Invalid response from server");
          }

          const etags: ETagPart[] = [];
          const maxConcurrentUploads = 5; // Maximum 5 concurrent chunk uploads
          let completedChunks = 0; // Track completed chunks for accurate progress

          // Upload chunks in batches of maxConcurrentUploads
          for (let i = 0; i < partItems.length; i += maxConcurrentUploads) {
            const batch = partItems.slice(i, i + maxConcurrentUploads);

            await Promise.all(
              batch.map(async (partItem: any) => {
                const { number, size, upload_url } = partItem;
                const start = partItems
                  .filter((item: { number: number }) => item.number < number)
                  .reduce((total: number, item: { size: number }) => total + item.size, 0);
                const end = Math.min(start + size, fileItem.file.size);
                const fileChunk = fileItem.file.slice(start, end);

                if (!upload_url) {
                  throw new Error(`No pre-signed URL for chunk ${number}`);
                }

                const chunkInfo: ChunkInfo = {
                  fileChunk,
                  fileType: fileItem.file.type,
                  preSignedUrl: upload_url,
                };

                const etag = await uploadChunk(chunkInfo, fileItem.abortController!);

                etags.push({ etag, number });

                // Update progress based on actual completed chunks
                completedChunks++;
                const progress = Math.round((completedChunks * 100) / partItems.length);
                setFiles((prev) =>
                  prev.map((f) => (f.id === fileItem.id ? { ...f, progress } : f))
                );
                onProgress?.(fileItem.id, progress);
              })
            );
          }

          // Complete multipart upload
          await completeMultipartUploadMutation.mutateAsync({
            parentId: parentId || null,
            upload_id: uploadId,
            part_etags: etags,
          });

          setFiles((prev) =>
            prev.map((f) => (f.id === fileItem.id ? { ...f, status: "success", progress: 100 } : f))
          );
          onProgress?.(fileItem.id, 100);
          resolve();
        } catch (error: any) {
          const errorMessage = error?.message || "Failed to upload file";
          setFiles((prev) =>
            prev.map((f) =>
              f.id === fileItem.id
                ? {
                    ...f,
                    status: "error",
                    progress: 0,
                    error: errorMessage,
                  }
                : f
            )
          );
          onError?.(errorMessage);
          reject(error);
        }
      });
    },
    [initMultipartUploadMutation, completeMultipartUploadMutation, onProgress, onError]
  );

  // Add files
  const addFiles = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const fileList = Array.from(e.target.files || []);
      const errors: string[] = [];
      const validFiles: FileUploadItem[] = [];
      if (fileList.length > maxFiles) {
        errors.push(`最多只能选择 ${maxFiles} 个文件`);
        setValidationErrors(errors);
        return [];
      }

      fileList.forEach((file) => {
        // 验证文件类型
        if (!validateFileType(file, supportedFileTypes)) {
          errors.push(`文件 "${file.name}" 类型不支持`);
          return;
        }

        // 验证文件大小
        if (!validateFileSize(file, fileSizeLimit, tableFileSizeLimit)) {
          errors.push(`文件 "${file.name}" 大小超过限制`);
          return;
        }

        validFiles.push({
          file,
          id: generateId(),
          status: "pending",
          progress: 0,
          abortController: new AbortController(),
        });
      });

      setValidationErrors(errors);
      setFiles((prev) => [...prev, ...validFiles]);

      if (autoUpload && errors.length === 0 && validFiles.length > 0) {
        uploadFiles(validFiles);
      }
      // 清空文件，防止重名文件取消后不能重新选择
      e.target.value = "";
      return validFiles;
    },
    [maxFiles, generateId, autoUpload]
  );

  // Upload multiple files
  const uploadFiles = useCallback(
    async (filesToUpload?: FileUploadItem[]) => {
      const finalFiles = filesToUpload || files;
      const targetFiles = finalFiles.filter((f) => f.status === "pending" || f.status === "error");

      if (targetFiles.length === 0) return;

      try {
        const uploadTracker = new Map<string, { completed: boolean; error?: string }>();
        targetFiles.forEach((file) => uploadTracker.set(file.id, { completed: false }));

        const MAX_CONCURRENT_UPLOADS = 5;
        const uploadQueue = [...targetFiles];
        let activeUploads: Promise<void>[] = [];

        while (uploadQueue.length > 0 || activeUploads.length > 0) {
          while (uploadQueue.length > 0 && activeUploads.length < MAX_CONCURRENT_UPLOADS) {
            const file = uploadQueue.shift()!;
            activeUploads.push(uploadSingleFile(file));
          }

          if (activeUploads.length > 0) {
            const results = await Promise.allSettled(activeUploads);
            results.forEach((result) => {
              if (result.status === "rejected") {
                console.error("File upload failed:", result.reason);
              }
            });
            activeUploads = [];
          }
        }

        const errors: string[] = [];
        uploadTracker.forEach((status, fileId) => {
          if (status.error) {
            errors.push(`File ${fileId} upload failed: ${status.error}`);
          }
        });

        if (errors.length > 0) {
          onError?.(errors.join("\n"));
        }

        const successFiles = files.filter((f) => f.status === "success");
        if (successFiles.length > 0) {
          onSuccess?.(successFiles);
        }
      } catch (error) {
        toast({
          title: "上传失败，请稍后重试",
          variant: "destructive",
        });
        console.error("Batch upload failed:", error);
        onError?.("批量上传失败");
      }
    },
    [files, uploadSingleFile, onSuccess, onError]
  );

  // Retry failed file
  const retryFile = useCallback(
    (fileId: string) => {
      const fileItem = files.find((f) => f.id === fileId);
      if (fileItem && fileItem.status === "error") {
        uploadSingleFile(fileItem);
      }
    },
    [files, uploadSingleFile]
  );

  // Remove file
  const removeFile = useCallback((fileId: string) => {
    setFiles((prev) => {
      const fileItem = prev.find((f) => f.id === fileId);
      if (fileItem && fileItem.status === "uploading") {
        fileItem.abortController?.abort("用户取消");
      }
      return prev.filter((f) => f.id !== fileId);
    });
  }, []);

  // Clear all files
  const clearFiles = useCallback(() => {
    setFiles([]);
    setValidationErrors([]);
  }, []);

  // Stats
  const stats = {
    total: files.length,
    pending: files.filter((f) => f.status === "pending").length,
    uploading: files.filter((f) => f.status === "uploading").length,
    success: files.filter((f) => f.status === "success").length,
    error: files.filter((f) => f.status === "error").length,
    isUploading: files.some((f) => f.status === "uploading"),
    hasErrors: files.some((f) => f.status === "error"),
    hasSuccess: files.some((f) => f.status === "success"),
  };

  return {
    files,
    validationErrors,
    stats,
    addFiles,
    uploadFiles,
    retryFile,
    removeFile,
    clearFiles,
    isUploading: files.some((f) => f.status === "uploading"),
  };
}

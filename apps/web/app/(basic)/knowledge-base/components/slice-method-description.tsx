"use client";

import { SliceMethodType } from "@/service/knowledge-base-service";
import { parseMethodAtom } from "@/store/kbase-store";
import { cn } from "@ragtop-web/ui/lib/utils";
import { useAtomValue } from "jotai";
import { useTranslations } from "next-intl";

// 辅助组件用于国际化文本
function SliceMethodDescriptionText({ methodType }: { methodType: string }) {
  const t = useTranslations("sliceMethod");

  if (methodType === "naive") {
    return <p>{t("naive.description")}</p>;
  }

  return null;
}

// 切片方法映射
export const sliceMethodOptions = [
  { value: "naive", label: "General" },
  { value: "qa", label: "Q&A" },
  { value: "resume", label: "Resume" },
  { value: "manual", label: "Manual" },
  { value: "table", label: "Table" },
  { value: "paper", label: "Paper" },
  { value: "book", label: "Book" },
  { value: "laws", label: "Laws" },
  { value: "presentation", label: "Presentation" },
  { value: "picture", label: "Picture" },
  { value: "one", label: "One" },
  { value: "audio", label: "Audio" },
  { value: "email", label: "Email" },
  { value: "tag", label: "Tag" },
  { value: "knowledgeGraph", label: "knowledgeGraph" },
];

// 切片方法说明内容
const sliceMethodDescriptions: Record<SliceMethodType, React.ReactNode> = {
  book: (
    <>
      {/* <p>
        由于一本书很长，并不是所有部分都有用，如果是 PDF,
        请为每本书设置<i>页面范围</i>，以消除负面影响并节省分析计算时间。</p> */}
    </>
  ),
  laws: (
    <>
      {/* <p>
        法律文件有非常严格的书写格式。 我们使用文本特征来检测分割点。
      </p><p>
        chunk的粒度与'ARTICLE'一致, 所有上层文本都会包含在chunk中。
      </p> */}
    </>
  ),
  manual: (
    <>
      {/* <p>
        假设手册具有分层部分结构。 使用最低的部分标题作为对文档进行切片的枢轴。
        因此，同一部分中的图和表不会被分割，并且块大小可能会很大。
      </p> */}
    </>
  ),
  naive: (
    <>
      <SliceMethodDescriptionText methodType="naive" />
      {/* <p>此方法将简单的方法应用于块文件：</p>
      <p>
        <li>系统将使用视觉检测模型将连续文本分割成多个片段。</li>
        <li>接下来，这些连续的片段被合并成Token数不超过“Token数”的块。</li></p> */}
    </>
  ),
  paper: (
    <>
      {/* <p>
        如果我们的模型运行良好，论文将按其部分进行切片，例如<i>摘要、1.1、1.2</i>等。</p><p>
        这样做的好处是LLM可以更好的概括论文中相关章节的内容，
        产生更全面的答案，帮助读者更好地理解论文。
        缺点是它增加了 LLM 对话的背景并增加了计算成本，
        所以在对话过程中，你可以考虑减少‘<b>topN</b>’的设置。</p> */}
    </>
  ),
  presentation: (
    <>
      {/* <p>
        每个页面都将被视为一个块。 并且每个页面的缩略图都会被存储。</p><p>
        <i>您上传的所有PPT文件都会使用此方法自动分块，无需为每个PPT文件进行设置。</i></p> */}
    </>
  ),
  qa: (
    <>
      {/* <li>
        如果文件是<b> excel </b>格式，则应由两个列组成
        没有标题：一个提出问题，另一个用于答案，
        答案列之前的问题列。多张纸是
        只要列正确结构，就可以接受。
      </li>
      <li>
        如果文件是<b> csv/txt </b>格式
        以 UTF-8 编码且用 TAB 作分开问题和答案的定界符。
      </li>
      <p>
        <i>
          未能遵循上述规则的文本行将被忽略，并且
          每个问答对将被认为是一个独特的部分。
        </i>
      </p> */}
    </>
  ),
  picture: (
    <>
      {/* <p>
        如果图片中有文字，则应用 OCR 提取文字作为其文字描述。
      </p><p>
        如果OCR提取的文本不够，可以使用视觉LLM来获取描述。
      </p> */}
    </>
  ),

  // one: (
  //   <>
  //     <p>支持的文件格式为<b>DOCX、EXCEL、PDF、TXT</b>。
  //     </p><p>
  //       对于一个文档，它将被视为一个完整的块，根本不会被分割。
  //     </p><p>
  //       如果你要总结的东西需要一篇文章的全部上下文，并且所选LLM的上下文长度覆盖了文档长度，你可以尝试这种方法。
  //     </p>
  //   </>
  // ),

  resume: (
    <>
      {/* <p>
        简历有多种格式，就像一个人的个性一样，但我们经常必须将它们组织成结构化数据，以便于搜索。
      </p><p>
        我们不是将简历分块，而是将简历解析为结构化数据。 作为HR，你可以扔掉所有的简历，
        您只需与<i>'RAGFlow'</i>交谈即可列出所有符合资格的候选人。
      </p> */}
    </>
  ),
  table: (
    <>
      {/* <p>
        以下是一些提示：
      </p>
      <ul>
        <li>对于 csv 或 txt 文件，列之间的分隔符为 <em><b>TAB</b></em>。</li>
        <li>第一行必须是列标题。</li>
        <li>列标题必须是有意义的术语，以便我们的大语言模型能够理解。
          列举一些同义词时最好使用斜杠<i>'/'</i>来分隔，甚至更好
          使用方括号枚举值，例如 <i>'gender/sex(male,female)'</i>.
          以下是标题的一些示例：
          <ol>
            <li>供应商/供货商<b>'TAB'</b>颜色（黄色、红色、棕色）<b>'TAB'</b>性别（男、女）<b>'TAB'</ b>尺码（M、L、XL、XXL）</li>
            <li>姓名/名字<b>'TAB'</b>电话/手机/微信<b>'TAB'</b>最高学历（高中，职高，硕士，本科，博士，初中，中技，中 专，专科，专升本，MPA，MBA，EMBA）</li>
          </ol>
        </li>
        <li>表中的每一行都将被视为一个块。</li>
      </ul> */}
    </>
  ),
  // knowledgeGraph: (
  //   <>
  //     <p>支持的文件格式为<b>DOCX、EXCEL、PPT、IMAGE、PDF、TXT、MD、JSON、EML</b>

  //       <p>文件分块后，使用分块提取整个文档的知识图谱和思维导图。此方法将简单的方法应用于分块文件：
  //         连续的文本将被切成大约 512 个 token 数的块。</p>
  //       <p>接下来，将分块传输到 LLM 以提取知识图谱和思维导图的节点和关系。</p>

  //       注意您需要指定的条目类型。</p>
  //   </>
  // ),
  // tag: (
  //   <>
  //     <p>使用“标签”作为分块方法的知识库应该被其他知识库使用，以将标签添加到其块中，对这些块的查询也将带有标签。</p>
  //     <p>使用“标签”作为分块方法的知识库<b>不</b>应该参与 RAG 过程。</p>
  //     <p>此知识库中的块是标签的示例，它们演示了整个标签集以及块和标签之间的相关性。</p>

  //     <p>此块方法支持<b>XLSX</b>和<b>CSV/TXT</b>文件格式。</p>
  //     <p>如果文件为<b>XLSX</b>格式，则它应该包含两列无标题：一列用于内容，另一列用于标签，内容列位于标签列之前。可以接受多个工作表，只要列结构正确即可。</p>
  //     <p>如果文件为 <b>CSV/TXT</b> 格式，则必须使用 UTF-8 编码并以 TAB 作为分隔符来分隔内容和标签。</p>
  //     <p>在标签列中，标签之间使用英文 <b>逗号</b>。</p>
  //     <i>不符合上述规则的文本行将被忽略，并且每对文本将被视为一个不同的块。</i>
  //   </>
  // ),
};

interface SliceMethodDescriptionProps {
  method: SliceMethodType;
  className?: string;
}

/**
 * 切片方法说明组件
 */
export function SliceMethodDescription({ method, className }: SliceMethodDescriptionProps) {
  const parseMethod = useAtomValue(parseMethodAtom);
  const supportedFilesTypes = parseMethod
    ?.find(({ parse_model }) => parse_model === method)
    ?.file_extensions.join("、");

  return (
    <div className={cn("text-muted-foreground text-sm", className)}>
      <p>该切片方法支持如下文件格式:</p>
      <b> {supportedFilesTypes}</b>
      {sliceMethodDescriptions[method]}
    </div>
  );
}

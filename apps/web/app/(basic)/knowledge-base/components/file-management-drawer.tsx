"use client";

import { Badge } from "@ragtop-web/ui/components/badge";
import { But<PERSON> } from "@ragtop-web/ui/components/button";
import { Checkbox } from "@ragtop-web/ui/components/checkbox";
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer";
import { Input } from "@ragtop-web/ui/components/input";
import { ScrollArea } from "@ragtop-web/ui/components/scroll-area";
import { Separator } from "@ragtop-web/ui/components/separator";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  FileText,
  Play,
  RefreshCw,
  Search,
  XCircle,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { type LocalKnowledgeBase } from "../page";

// 解析状态类型
export type ParseStatus = "success" | "failed" | "parsing" | "none";

// 扩展文件类型，添加解析状态
export type FileWithStatus = {
  id: string;
  name: string;
  type: string;
  size: string;
  parseStatus: ParseStatus;
};

interface FileManagementDrawerProps {
  open: boolean;
  onClose: () => void;
  knowledgeBase: LocalKnowledgeBase;
  onParseFiles: (fileIds: string[]) => void;
}

export function FileManagementDrawer({
  open,
  onClose,
  knowledgeBase,
  onParseFiles,
}: FileManagementDrawerProps) {
  const t = useTranslations("fileManagement");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([]);

  // 过滤文件
  const filteredFiles = knowledgeBase.files.filter((file) =>
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 处理文件选择
  const handleFileSelect = (fileId: string) => {
    if (selectedFileIds.includes(fileId)) {
      setSelectedFileIds(selectedFileIds.filter((id) => id !== fileId));
    } else {
      setSelectedFileIds([...selectedFileIds, fileId]);
    }
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedFileIds.length === filteredFiles.length) {
      setSelectedFileIds([]);
    } else {
      setSelectedFileIds(filteredFiles.map((file) => file.id));
    }
  };

  // 解析单个文件
  const handleParseFile = (fileId: string) => {
    onParseFiles([fileId]);
  };

  // 批量解析文件
  const handleBatchParse = () => {
    if (selectedFileIds.length > 0) {
      onParseFiles(selectedFileIds);
      setSelectedFileIds([]);
    }
  };

  // 渲染解析状态图标
  const renderStatusIcon = (status: ParseStatus) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "parsing":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "none":
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  // 获取解析状态文本
  const getStatusText = (status: ParseStatus) => {
    switch (status) {
      case "success":
        return t("status.success");
      case "failed":
        return t("status.failed");
      case "parsing":
        return t("status.parsing");
      case "none":
      default:
        return t("status.none");
    }
  };

  // 获取解析按钮文本
  const getParseButtonText = (status: ParseStatus) => {
    return status !== "none" ? t("parseButton.reparse") : t("parseButton.parse");
  };

  return (
    <CustomDrawer open={open} onClose={onClose} title={`${knowledgeBase.name} - ${t("title")}`}>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="text-primary h-5 w-5" />
            <h3 className="text-lg font-medium">{t("fileList")}</h3>
          </div>
          <Badge variant="outline" className="ml-2">
            {t("filesCount", { count: knowledgeBase.files.length })}
          </Badge>
        </div>

        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
            <Input
              type="search"
              placeholder={t("searchPlaceholder")}
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {selectedFileIds.length > 0 && (
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground text-sm">
              {t("selectedFiles", { count: selectedFileIds.length })}
            </span>
            <Button
              size="sm"
              variant="default"
              onClick={handleBatchParse}
              className="flex items-center gap-1"
            >
              <Play className="h-3.5 w-3.5" />
              {t("batchParse")}
            </Button>
          </div>
        )}

        <Separator />

        <ScrollArea className="h-[400px]">
          <div className="space-y-1">
            <div className="bg-muted/50 flex items-center rounded-md p-2">
              <Checkbox
                id="select-all"
                checked={
                  filteredFiles.length > 0 && selectedFileIds.length === filteredFiles.length
                }
                onCheckedChange={handleSelectAll}
                className="mr-2"
              />
              <div className="grid flex-1 grid-cols-12 text-sm font-medium">
                <span className="col-span-6">{t("fileName")}</span>
                <span className="col-span-3">{t("parseStatus")}</span>
                <span className="col-span-3 text-right">{t("actions")}</span>
              </div>
            </div>

            {filteredFiles.length === 0 ? (
              <div className="text-muted-foreground py-8 text-center">{t("noFilesFound")}</div>
            ) : (
              filteredFiles.map((file) => (
                <div key={file.id} className="hover:bg-muted flex items-center rounded-md p-2">
                  <Checkbox
                    id={`file-${file.id}`}
                    checked={selectedFileIds.includes(file.id)}
                    onCheckedChange={() => handleFileSelect(file.id)}
                    className="mr-2"
                  />
                  <div className="grid flex-1 grid-cols-12 items-center text-sm">
                    <div className="col-span-6 flex items-center">
                      <FileText className="text-primary mr-2 h-4 w-4" />
                      <span className="truncate">{file.name}</span>
                    </div>
                    <div className="col-span-3 flex items-center">
                      {renderStatusIcon(file.parseStatus)}
                      <span className="ml-1 text-xs">{getStatusText(file.parseStatus)}</span>
                    </div>
                    <div className="col-span-3 flex justify-end">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex h-7 items-center gap-1 text-xs"
                        onClick={() => handleParseFile(file.id)}
                      >
                        {file.parseStatus !== "none" ? (
                          <RefreshCw className="h-3 w-3" />
                        ) : (
                          <Play className="h-3 w-3" />
                        )}
                        {getParseButtonText(file.parseStatus)}
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </div>
    </CustomDrawer>
  );
}

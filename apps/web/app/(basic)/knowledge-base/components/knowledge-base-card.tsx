"use client";

import { checkIsOwner } from "@/lib/user-role";
import { KnowledgeBase } from "@/service/knowledge-base-service";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ragtop-web/ui/components/card";
import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";
import { IconButton } from "@ragtop-web/ui/components/icon-button";
import { BookOpenText, Pencil, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface KnowledgeBaseCardProps {
  knowledgeBase: KnowledgeBase;
  onClick: () => void;
  onDelete: () => void;
  onEdit: (knowledgeBase: KnowledgeBase, event: React.MouseEvent) => void;
}

export function KnowledgeBaseCard({
  knowledgeBase,
  onClick,
  onDelete,
  onEdit,
}: KnowledgeBaseCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const creator = knowledgeBase?.creator;
  const isOwner = checkIsOwner(creator?.id, creator?.admin);
  const t = useTranslations("knowledgeBase");

  // 处理打开删除确认对话框
  const handleOpenDeleteDialog = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // 如果不是owner，不执行删除操作
    if (!isOwner) {
      return;
    }
    setIsDeleteDialogOpen(true);
  };
  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    await onDelete();
    setIsDeleteDialogOpen(false);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.(knowledgeBase, e);
  };

  // 处理卡片点击事件，当删除对话框打开时禁用点击
  const handleCardClick = () => {
    if (!isDeleteDialogOpen) {
      onClick();
    }
  };

  return (
    <Card
      className="relative cursor-pointer transition-colors"
      onClick={handleCardClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardHeader className="flex w-full items-center justify-between gap-2">
        <CardTitle className="flex w-full min-w-0 flex-1 items-center gap-2 text-base font-medium">
          <BookOpenText className="text-primary h-4 w-4 flex-shrink-0" />
          <span className="block w-full truncate">{knowledgeBase.name}</span>
        </CardTitle>
        {/* 操作按钮 - 仅在悬停时显示 */}
        {isHovered && (
          <div className="flex flex-shrink-0 gap-1">
            {/* 删除按钮 */}
            <IconButton
              variant="ghost"
              tooltip={isOwner ? t("card.delete") : t("card.noPermissionDelete")}
              disabled={!isOwner}
              onClick={handleOpenDeleteDialog}
            >
              <Trash2 className="text-destructive h-3 w-3" />
            </IconButton>

            <IconButton variant="ghost" tooltip={t("card.edit")} onClick={handleEdit}>
              <Pencil className="h-3 w-3" />
            </IconButton>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="text-muted-foreground text-sm">
          <p>
            <span className="font-medium">{t("card.description")}</span> {knowledgeBase.description}
          </p>
          <p>
            <span className="font-medium">{t("card.fileCount")}</span>{" "}
            {t("card.filesCount", { count: knowledgeBase.doc_num || 0 })}
          </p>
          <p>
            <span className="font-medium">{t("card.creator")}</span> {knowledgeBase.creator?.name}
          </p>
        </div>
      </CardContent>

      {/* 删除确认对话框 */}
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDelete}
        title={t("delete.title")}
        description={t("delete.description", { name: knowledgeBase?.name })}
      />
    </Card>
  );
}

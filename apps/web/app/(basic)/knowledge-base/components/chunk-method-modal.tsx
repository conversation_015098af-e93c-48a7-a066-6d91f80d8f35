"use client";

import { KnowledgeBaseDocList } from "@/service/knowledge-base-doc-service";
import { SliceMethodType } from "@/service/knowledge-base-service";
import { fileExtensionAtom } from "@/store/kbase-store";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ragtop-web/ui/components/select";
import { Slider } from "@ragtop-web/ui/components/slider";
import { useAtomValue } from "jotai";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { SliceMethodDescription, sliceMethodOptions } from "./slice-method-description";

// 表单验证模式
const formSchema = z.object({
  sliceMethod: z.string().min(1, "切片方法不能为空"),
  chunk_token_num: z.number().min(128, "Top N不能小于128").optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface ChunkMethodModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: FormValues & { fileId: string }) => void;
  file: KnowledgeBaseDocList;
}

/**
 * 切片方法设置模态框
 */
export function ChunkMethodModal({ open, onClose, onSubmit, file }: ChunkMethodModalProps) {
  const [selectedMethod, setSelectedMethod] = useState<SliceMethodType | null>(null);
  const [inputValue, setInputValue] = useState("512");
  const fileExtension = useAtomValue(fileExtensionAtom);
  const { id: fileId, name: fileName, chunk_config = {} } = file;
  const initialMethod = chunk_config?.chunk_provider_id;
  const initialTokenNum = chunk_config?.chunk_token_num;
  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      sliceMethod: initialMethod,
      chunk_token_num: initialTokenNum || undefined,
    },
  });

  // 监听chunk_token_num变化，保持inputValue同步
  const chunkTokenNum = form.watch("chunk_token_num");
  useEffect(() => {
    setInputValue(chunkTokenNum?.toString() || "512");
  }, [chunkTokenNum]);

  const filteredOptions = sliceMethodOptions.filter(({ value }) => {
    if (file?.type) {
      return fileExtension[file.type]?.includes(value);
    }
  });

  // 监听选择的切片方法变化
  const watchSliceMethod = form.watch("sliceMethod") as SliceMethodType;

  useEffect(() => {
    if (watchSliceMethod) {
      setSelectedMethod(watchSliceMethod);
    }
  }, [watchSliceMethod]);

  // 处理表单提交
  const handleSubmit = (values: FormValues) => {
    const finalValue = { ...values, fileId };
    onSubmit(finalValue);
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>设置切片方法</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="text-muted-foreground mb-4 text-sm">
              为文件 <span className="font-medium">{fileName}</span> 设置切片方法
            </div>

            <FormField
              control={form.control}
              name="sliceMethod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>切片方法</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="选择切片方法" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {filteredOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>选择文档切片的方法</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            {watchSliceMethod === "naive" && (
              <FormField
                control={form.control}
                name="chunk_token_num"
                render={({ field }) => {
                  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
                    const value = e.target.value;
                    setInputValue(value);

                    // 允许空值，不立即验证
                    if (value === "") {
                      return;
                    }

                    const numValue = parseInt(value, 10);
                    if (!isNaN(numValue)) {
                      field.onChange(numValue);
                    }
                  };

                  const handleInputBlur = () => {
                    // 失去焦点时进行范围验证和修正
                    const numValue = parseInt(inputValue, 10);
                    if (isNaN(numValue) || numValue < 128) {
                      const correctedValue = 128;
                      setInputValue(correctedValue.toString());
                      field.onChange(correctedValue);
                    } else if (numValue > 2048) {
                      const correctedValue = 2048;
                      setInputValue(correctedValue.toString());
                      field.onChange(correctedValue);
                    }
                  };
                  return (
                    <FormItem>
                      <div className="mb-2 flex items-center justify-between">
                        <FormLabel>切片大小</FormLabel>
                        <div className="flex items-center gap-2">
                          <Input
                            type="number"
                            min={128}
                            max={2048}
                            value={inputValue}
                            onChange={handleInputChange}
                            onBlur={handleInputBlur}
                            className="h-8 w-20 text-sm"
                          />
                        </div>
                      </div>
                      <FormControl>
                        <div className="flex w-full items-center gap-2">
                          <span className="text-muted-foreground w-8 text-left text-xs">128</span>
                          <Slider
                            value={[chunkTokenNum || 512]}
                            min={128}
                            max={2048}
                            step={1}
                            onValueChange={(value) => {
                              const newValue = value[0];
                              if (newValue !== undefined) {
                                field.onChange(newValue);
                                setInputValue(newValue.toString());
                              }
                            }}
                            className="flex-1"
                          />
                          <span className="text-muted-foreground w-8 text-right text-xs">2048</span>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            )}

            {/* 切片方法说明 */}
            {selectedMethod && (
              <div className="bg-muted/30 rounded-md border p-3">
                {/* <h4 className="font-medium mb-2">方法说明</h4> */}
                <SliceMethodDescription method={selectedMethod} />
              </div>
            )}
            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                取消
              </Button>
              <Button type="submit">确认</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

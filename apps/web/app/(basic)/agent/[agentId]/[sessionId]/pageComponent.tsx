"use client";

import { ChatContainer } from "@/components/chat/chat-container";
import { useAgent } from "@/service/agent-service";
import { useSession } from "@/service/session-service";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface SessionPageComponentProps {
  agentId: string;
  sessionId: string;
}

/**
 * Session详情页面组件
 * 集成真实的API调用，支持获取Agent、Session和历史消息
 */
export default function SessionPageComponent({ agentId, sessionId }: SessionPageComponentProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const router = useRouter();
  const t = useTranslations("agent");
  // API hooks - 延迟加载
  const { data: agent, isLoading: isAgentLoading } = useAgent(agentId, isInitialized);
  const {
    data: session,
    isLoading: isSessionLoading,
    isError,
  } = useSession(sessionId, isInitialized);

  // 页面初始化时触发数据加载
  useEffect(() => {
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (isError) {
      router.push("/");
    }
  }, [isError]);

  // 构建聊天标题
  const chatTitle = agent && session ? `${agent.name} - ${session.title}` : t("common.chat");

  // 构建面包屑导航
  const breadcrumbs = [
    { title: t("common.agentList"), href: "/", isCurrent: false, linkComponent: Link },
    {
      title: agent?.name || "Agent",
      href: `/agent/${agentId}`,
      isCurrent: false,
      linkComponent: Link,
    },
    {
      title: session?.title || t("session.title"),
      href: `/agent/${agentId}/${sessionId}`,
      isCurrent: true,
    },
  ];

  if (isAgentLoading || isSessionLoading) {
    return (
      <CustomContainer
        title={t("common.loading")}
        // breadcrumbs={breadcrumbs}
        className="mx-auto h-[calc(100vh-8rem)] max-w-5xl py-3"
      >
        <div className="flex h-64 items-center justify-center">
          <div className="flex flex-col items-center gap-2">
            <div className="text-center">
              <Loader2 className="mx-auto mb-4 h-8 w-8 animate-spin text-gray-900" />
              <p className="text-gray-600">{t("common.loadingMessage")}...</p>
            </div>
          </div>
        </div>
      </CustomContainer>
    );
  }

  if (!agent || !session) {
    return (
      <CustomContainer
        title="会话不存在"
        breadcrumbs={breadcrumbs}
        className="mx-auto h-[calc(100vh-8rem)] max-w-5xl py-3"
      >
        <div className="flex h-64 items-center justify-center">
          <p className="text-muted-foreground">未找到指定的会话</p>
        </div>
      </CustomContainer>
    );
  }

  return (
    <CustomContainer
      title={chatTitle}
      breadcrumbs={breadcrumbs}
      className="mx-auto h-[calc(100vh-8rem)] max-w-5xl py-3"
    >
      {!agent.settings?.llm_model_name && (
        <p className="text-destructive">{t("common.modelNotConfigured")}</p>
      )}

      <ChatContainer
        key={sessionId}
        agentName={agent?.name || t("common.assistant")}
        sessionId={sessionId}
        agentId={agentId}
      />
    </CustomContainer>
  );
}

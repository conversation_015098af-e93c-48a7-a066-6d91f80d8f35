"use client";

import { AgentDetails } from "@/components/agent/agent-details";
import { AgentDialog } from "@/components/agent/agent-dialog";
import { checkIsOwner, useIsTeamAdmin } from "@/lib/user-role";
import { formatDateTime } from "@/lib/utils";
import { useTranslations } from "next-intl";

import { useAgent, useDeleteAgent, useUpdateAgent } from "@/service/agent-service";
import { Button } from "@ragtop-web/ui/components/button";
import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { Loader2, Pencil, Trash2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface AgentPageComponentProps {
  agentId: string;
}

/**
 * Agent详情页面组件
 * 集成真实的API调用，支持获取、更新和删除Agent
 */
export default function AgentPageComponent({ agentId }: AgentPageComponentProps) {
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations("agent");
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // API hooks - 延迟加载
  const { data: agent, isFetching: isLoading, isError } = useAgent(agentId, isInitialized);
  const updateAgent = useUpdateAgent(agentId);
  const deleteAgent = useDeleteAgent();
  const isOwner = checkIsOwner(agent?.creator?.id, agent?.creator?.admin);
  const isAdmin = useIsTeamAdmin();

  // 判断是否能编辑Agent
  // 1. 如果是个人agent，默认都可以操作
  // 2. 如果是团队agent，只有owner并且是管理员可以操作删除
  // 3. 如果是团队agent，管理员就可以操作编辑
  const canEditAgent = agent?.scope === "PRIVATE" || isAdmin;
  const canDeleteAgent = agent?.scope === "PRIVATE" || (isOwner && isAdmin);

  // 页面初始化时触发数据加载
  useEffect(() => {
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (isError) {
      router.push("/");
    }
  }, [isError]);

  // 处理更新Agent
  const handleUpdateAgent = async (data: any) => {
    try {
      await updateAgent.mutateAsync({ ...data, agent_id: agentId });
      toast({
        title: t("tips.success"),
        description: t("tips.updateSuccess"),
      });
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error("Failed to update agent:", error);
      toast({
        title: t("common.error"),
        description: t("tips.updateFailed"),
        variant: "destructive",
      });
    }
  };

  // 处理删除Agent
  const handleDeleteAgent = async () => {
    try {
      await deleteAgent.mutateAsync({ agent_id: agentId });
      toast({
        title: t("tips.success"),
        description: t("tips.deleteSuccess"),
      });
      setIsDeleteDialogOpen(false);
      router.push("/");
    } catch (error) {
      console.error("Failed to delete agent:", error);
      toast({
        title: t("common.error"),
        description: t("tips.deleteFailed"),
        variant: "destructive",
      });
    }
  };

  // 构建面包屑导航
  const breadcrumbs = [
    { title: t("common.agentList"), href: "/", isCurrent: false, linkComponent: Link },
    {
      title: agent?.name || t("common.untitled"),
      href: `/agent/${agentId}`,
      isCurrent: true,
    },
  ];

  if (isLoading) {
    return (
      <CustomContainer title={t("common.loading")} breadcrumbs={breadcrumbs}>
        <div className="flex h-64 items-center justify-center">
          <div className="flex flex-col items-center gap-2">
            <div className="border-primary h-6 w-6 animate-spin rounded-full border-2 border-t-transparent"></div>
            <p className="text-muted-foreground text-sm">{t("common.loadingMessage")}</p>
          </div>
        </div>
      </CustomContainer>
    );
  }

  if (!agent) {
    return (
      <CustomContainer title={t("common.notFound")} breadcrumbs={breadcrumbs}>
        <div className="flex h-64 items-center justify-center">
          <p className="text-muted-foreground">{t("common.notFoundMessage")}</p>
        </div>
      </CustomContainer>
    );
  }

  return (
    <CustomContainer
      title={agent?.name || t("common.untitled")}
      breadcrumbs={breadcrumbs}
      action={
        <div className="flex gap-2">
          <Button
            size="sm"
            onClick={() => setIsEditDialogOpen(true)}
            disabled={!canEditAgent || updateAgent.isPending || deleteAgent.isPending}
          >
            {updateAgent.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Pencil className="h-4 w-4" />
            )}
            {t("actions.edit")}
          </Button>
          <Button
            size="sm"
            onClick={() => setIsDeleteDialogOpen(true)}
            variant="destructive"
            disabled={!canDeleteAgent || updateAgent.isPending || deleteAgent.isPending}
          >
            {deleteAgent.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
            {t("actions.delete")}
          </Button>
        </div>
      }
    >
      {agent?.scope === "TEAM_PUBLIC" && (
        <div className="space-y-3">
          {/* 知识库信息 */}
          <div className="text-muted-foreground bg-muted/30 mb-3 flex items-center gap-6 rounded-lg p-4 text-sm">
            <div className="flex items-center gap-2">
              <span className="font-medium">{t("meta.createTime")}</span>
              {formatDateTime(agent?.create_time || "")}
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">{t("meta.creator")}</span>
              {agent?.creator?.name}
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">{t("meta.updateTime")}</span>
              {formatDateTime(agent?.update_time || "")}
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">{t("meta.updator")}</span>
              {agent?.updator?.name}
            </div>
          </div>
        </div>
      )}
      <AgentDetails agent={agent} />
      {isEditDialogOpen && agent && (
        <AgentDialog
          open={isEditDialogOpen}
          onClose={() => setIsEditDialogOpen(false)}
          onSubmit={handleUpdateAgent}
          initialData={agent}
        />
      )}
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDeleteAgent}
        title={t("delete.confirmTitle")}
        description={t("delete.confirmDescription")}
      />
    </CustomContainer>
  );
}

/**
 * i18n 配置文件
 * 用于 next-intl 和 i18n-ally 插件
 */

export const locales = ['zh', 'en'];
export const defaultLocale = 'zh';

export const namespaces = [
  'agent',
  'knowledgeBase',
  'dataset',
  'home',
  'fileManagement',
  'forms',
  'sliceMethod',
  'sidebar',
  'files',
  'common'
];

export const pathMatcher = '{locale}.json';
export const localesPaths = ['apps/web/messages'];

export default {
  locales,
  defaultLocale,
  namespaces,
  pathMatcher,
  localesPaths
};

{"agent": {"session": {"title": "会话", "notFound": "会话不存在", "notFoundMessage": "未找到指定的会话", "loadingSessionInfo": "加载会话信息中..."}, "tips": {"success": "成功", "updateSuccess": "Agent更新成功", "deleteSuccess": "Agent删除成功", "updateFailed": "更新Agent失败", "deleteFailed": "删除Agent失败"}, "actions": {"edit": "编辑", "delete": "删除", "editAgent": "编辑Agent", "deleteAgent": "删除Agent", "batchDeleteSessions": "批量删除Session", "clearSessions": "清空Session"}, "common": {"error": "错误", "loading": "加载中...", "loadingMessage": "加载Agent信息中...", "notFound": "Agent不存在", "notFoundMessage": "未找到指定的Agent", "untitled": "未命名Agent", "agentList": "Agent列表", "chat": "聊天", "assistant": "助手", "modelNotConfigured": "Agent未配置模型, 不能进行会话,请选择配置模型"}, "meta": {"createTime": "创建时间:", "creator": "创建人:", "updateTime": "修改时间:", "updator": "修改人:"}, "delete": {"confirmTitle": "确认删除", "confirmDescription": "您确定要删除这个Agent吗？此操作无法撤销，所有相关的会话和数据都将被永久删除。", "personalDescription": "您确定要删除这个Agent吗？此操作无法撤销，所有相关的会话和数据都将被永久删除。", "teamDescription": "您确定要删除这个Agent吗？团队agent删除后，其余使用这个agent的用户的相关会话和数据也会被永久删除。", "confirmText": "删除", "cancelText": "取消", "success": "Agent删除成功", "failed": "删除Agent失败"}, "batchDelete": {"title": "确认批量删除", "description": "您确定要删除选中的 {count} 个会话吗？此操作无法撤销，所有选中的会话都将被永久删除。", "confirmText": "确认删除", "cancelText": "取消", "success": "成功删除 {count} 个会话", "failed": "批量删除会话失败", "noSelection": "请选择要删除的会话", "error": "错误"}, "clear": {"title": "确认清空", "description": "清空{itemType}后，所有相关的{itemType}都将被永久删除。", "confirmText": "确认清空", "cancelText": "取消", "success": "Session清空成功", "failed": "清空会话失败"}, "dialog": {"title": "聊天配置", "description": "在这里，为你的专业知识库配置专属助手！", "cancel": "取消", "confirm": "确定"}, "form": {"resourceType": "资源类型", "knowledgeBase": "知识库", "datasets": "数据集", "database": "数据库", "file": "文件", "name": "Agent名称", "namePlaceholder": "测试Agent", "description": "Agent描述", "descriptionPlaceholder": "请输入关于Agent的简要介绍", "emptyReply": "空回复", "emptyReplyPlaceholder": "e.g. 抱歉，我无法回答这个问题。", "greeting": "设置开场白", "greetingPlaceholder": "你好！我是你的助手，有什么可以帮到你的吗？", "historyContext": "历史消息上下文(条数)", "supportedLanguages": "支持的语言", "supportedLanguagesPlaceholder": "请选择支持的语言", "supportedLanguagesDescription": "选择Agent支持的语言，至少选择一种语言", "agentPermission": "Agent权限", "personal": "个人", "team": "团队", "systemPrompt": "系统提示词", "systemPromptPlaceholder": "e.g. 你是一个学术领域的专家，请根据知识库的内容来尽可能详细的回答问题。", "summaryPrompt": "总结提示词", "summaryPromptPlaceholder": "e.g. 请根据数据集的内容来总结数据集。", "similarityThreshold": "相似度阈值", "keywordWeight": "关键字相似度权重", "topN": "Top N", "model": "模型", "modelPlaceholder": "请选择模型", "temperature": "温度", "topP": "Top P", "validation": {"resourceTypeRequired": "请选择资源类型", "resourceRequired": "请选择资源", "nameRequired": "Agent名称不能为空", "languageRequired": "请至少选择一种语言", "permissionRequired": "请选择Agent权限", "modelRequired": "请选择模型"}, "modelNotConfiguredMessage": "团队没有配置{modelType}模型，请联系管理员进行配置", "chatModel": "聊天", "datasetChatModel": "数据集聊天"}, "tabs": {"basicSettings": "基础设置", "promptEngine": "提示引擎", "modelSettings": "模型设置"}, "details": {"basicInfo": "基本信息", "agentName": "Agent姓名", "resourceType": "资源类型", "agentPermission": "Agent权限", "supportedLanguages": "支持的语言", "agentDescription": "Agent描述", "greeting": "开场白", "emptyReply": "空回复", "historyContext": "历史消息上下文(条数)", "promptSettings": "提示词设置", "systemPrompt": "系统提示词", "summaryPrompt": "总结提示词", "retrievalSettings": "检索设置", "similarityThreshold": "相似度阈值", "keywordWeight": "关键词权重", "retrievalCount": "检索数量", "modelConfig": "模型配置", "model": "模型", "temperature": "温度", "topP": "Top P", "noDescription": "无描述", "notSet": "无设置", "notConfigured": "未配置", "none": "无", "notSetYet": "暂未设置", "resourceTypes": {"knowledgeBase": "知识库", "databaseDataset": "数据库数据集", "fileDataset": "文件数据集"}}}, "session": {"title": "会话", "notFound": "会话不存在", "notFoundMessage": "未找到指定的会话", "loadingSessionInfo": "加载会话信息中..."}, "team": {"title": "团队成员", "addMember": "添加成员", "accountName": "账户名", "role": "角色", "actions": "操作", "admin": "管理员", "member": "成员", "delete": "删除", "deleteMemberTip": "删除成员", "addMembers": {"title": "添加团队成员", "description": "从现有账户中选择要添加到团队的成员", "addButton": "添加成员", "searchPlaceholder": "搜索成员...", "emptyText": "未找到匹配的成员", "loadingText": "加载中...", "endText": "已到达列表底部", "selectedCount": "已选择 {count} 名成员", "unknownUser": "未知用户"}, "deleteMember": {"title": "确认删除", "description": "您确定要删除成员 \"{memberName}\" 吗？此操作无法撤销。"}, "messages": {"addSuccess": "成员添加成功", "addFailed": "添加成员失败", "deleteSuccess": "成员删除成功", "deleteFailed": "删除成员失败", "fetchFailed": "获取可添加成员失败"}}, "model": {"title": "设置默认模型", "description": "使用前必须选择需要的模型", "chatModel": "聊天模型", "embeddingModel": "嵌入模型", "rerankModel": "<PERSON><PERSON>模型", "nl2sqlModel": "NL2SQL模型", "nl2pythonModel": "NL2PYTHON模型", "placeholder": "请选择适当的模型", "cancel": "取消", "confirm": "确认", "submitting": "提交中...", "validation": {"selectModel": "请选择适当的模型", "selectChatModel": "请选择适当的聊天模型", "selectEmbeddingModel": "请选择适当的嵌入模型"}}, "knowledgeBase": {"title": "知识库管理", "addNew": "添加新知识库", "loading": "加载中...", "loadError": "加载知识库列表失败: {error}", "retry": "重试", "unknownError": "未知错误", "create": "创建知识库", "saveChanges": "保存修改", "card": {"description": "知识库描述:", "fileCount": "文件数量:", "filesCount": "{count} 个文件", "creator": "创建人:", "edit": "编辑", "delete": "删除", "noPermissionDelete": "您没有权限删除"}, "delete": {"title": "确认删除", "description": "如果知识库已绑定Agent，对应的绑定关系也会被删除，您确定要删除 \"{name}\" 吗？此操作无法撤销。"}, "form": {"name": "知识库名称", "nameRequired": "知识库名称不能为空", "description": "知识库描述", "defaultSliceMethod": "文档默认切片方法", "sliceMethodRequired": "切片方法不能为空", "sliceMethodDescription": "选择文档切片的方法", "pdfParser": "PDF解析器", "usesDeepDoc": "使用 DeepDOC 解析 PDF 文档"}, "fileSelection": {"title": "为知识库 \"{name}\" 选择文件", "description": "请选择要添加到知识库的文件", "selectedCount": "已选择 {count} 个文件", "confirm": "确认添加", "searchPlaceholder": "搜索文件...", "emptyText": "没有找到文件", "loadingText": "加载中...", "endText": "已加载全部文件", "rootDirectory": "根目录", "breadcrumbHome": "首页"}, "details": {"title": "知识库详情", "backToList": "知识库列表", "notFound": "未找到指定的知识库", "loading": "加载中...", "loadingError": "加载知识库详情失败", "addFiles": "添加文件", "fileName": "文件名", "fileSize": "文件大小", "chunkCount": "分块数", "sliceMethod": "切片方法", "chunkSize": "分块大小", "status": "状态", "progress": "进度", "updateTime": "更新时间", "actions": "操作", "selectAll": "全选", "batchDelete": "批量删除", "batchParse": "批量解析", "batchStop": "批量停止", "batchStart": "批量开始", "noSelection": "请先选择要操作的文件", "selectedFilesCount": "已选择 {count} 项", "clearSelection": "清空选择", "createTime": "创建时间", "defaultSliceMethod": "默认切片方法", "documentCount": "文档数量", "creator": "创建人", "notSet": "未设置", "successTitle": "成功", "errorTitle": "错误", "noFilesToParse": "没有可解析的文件", "filesInParseQueue": "已将 {count} 个文件加入解析队列", "batchOperationFailed": "批量操作失败，请重试", "noFilesToStop": "没有正在解析的文件", "batchStopSuccess": "{count} 个文件正在取消解析中", "addFilesSuccess": "已成功添加 {count} 个文件到知识库", "addFilesFailed": "添加文件时发生错误，请重试", "stopParseSuccess": "已停止解析文件", "startParseSuccess": "已开始解析文件", "operationFailed": "操作失败，请重试", "documentDisabled": "已禁用文档", "documentEnabled": "已启用文档", "chunkMethodSetSuccess": "切片方法设置成功", "chunkMethodSetFailed": "设置失败，请重试", "noPermissionBatchDelete": "您没有删除这些文件的权限", "defaultKnowledgeBaseName": "知识库", "cannotSetChunkMethod": "当前状态下不能执行设置切片方法操作", "setChunkMethod": "设置切片方法", "enabled": "启用状态", "createdBy": "创建人", "createTime2": "创建时间", "updatedBy": "修改人", "updateTime2": "修改时间", "deleteFile": "删除文件", "noPermissionDelete": "您没有权限删除", "deleteFileSuccess": "文件删除成功", "deleteFileFailed": "删除文件失败", "batchDeleteSuccess": "批量删除成功，共删除 {count} 个文件", "batchDeleteFailed": "批量删除失败", "cannotParseWhileCancelling": "取消中，不能执行解析操作", "stopParse": "停止解析", "retryParse": "重试解析", "parseFile": "解析文件"}, "status": {"success": "成功", "failed": "失败", "parsing": "解析中", "none": "未处理", "unknown": "未知状态", "parseSuccess": "解析成功", "parseFailed": "解析失败", "queueing": "排队中", "parsingProgress": "解析中({percent})", "unparsed": "未解析", "cancelling": "取消中", "cancelled": "解析已取消"}, "operations": {"start": "开始", "stop": "停止", "delete": "删除", "settings": "设置", "refresh": "刷新"}, "messages": {"createSuccess": "知识库创建成功", "createFailed": "创建知识库失败", "updateSuccess": "知识库更新成功", "updateFailed": "更新知识库失败", "deleteSuccess": "知识库删除成功", "deleteFailed": "删除知识库失败", "addFilesSuccess": "文件添加成功", "addFilesFailed": "添加文件失败", "deleteFileSuccess": "文件删除成功", "deleteFileFailed": "删除文件失败", "batchDeleteSuccess": "批量删除成功", "batchDeleteFailed": "批量删除失败", "startParseSuccess": "解析任务已启动", "startParseFailed": "启动解析失败", "stopParseSuccess": "解析任务已停止", "stopParseFailed": "停止解析失败", "batchStartSuccess": "批量启动成功", "batchStartFailed": "批量启动失败", "batchStopSuccess": "批量停止成功", "batchStopFailed": "批量停止失败"}, "deleteDialog": {"title": "确认删除", "singleFile": "您确定要删除文件 \"{fileName}\" 吗？", "multipleFiles": "您确定要删除选中的 {count} 个文件吗？", "description": "此操作无法撤销。"}, "chunkMethod": {"title": "切片方法设置", "currentMethod": "当前切片方法: {method}", "selectNewMethod": "选择新的切片方法", "save": "保存", "cancel": "取消"}}, "datasets": {"title": "数据集管理", "fileDataset": "文件数据集", "databaseDataset": "数据库数据集", "addFileDataset": "添加文件数据集", "addDatabaseDataset": "添加数据库数据集", "loading": "加载中...", "loadError": "加载数据集列表失败: {error}", "retry": "重试", "unknownError": "未知错误", "create": "创建数据集", "saveChanges": "保存修改", "emptyMessage": "暂无数据集，点击上方按钮创建第一个数据集", "comingSoon": "此功能即将上线，敬请期待～", "form": {"name": "数据集名称", "nameRequired": "数据集名称不能为空", "description": "数据集描述", "descriptionRequired": "数据集描述不能为空", "username": "用户名", "usernameRequired": "用户名不能为空", "password": "密码", "passwordRequired": "密码不能为空", "database": "数据库名称", "databaseRequired": "数据库名称不能为空", "testConnection": "测试连接", "connectionSuccess": "连接成功！数据库可以正常访问。", "connectionError": "连接发生错误，请稍后重试。", "testing": "测试中...", "mustTestFirst": "请先测试连接成功后再保存", "create": "创建数据集", "update": "更新数据集"}, "messages": {"createSuccess": "创建成功", "datasetCreated": "数据集已创建", "createFailed": "创建失败", "createError": "创建数据集时发生错误", "updateSuccess": "更新成功", "datasetUpdated": "数据集已更新", "updateFailed": "更新失败", "updateError": "更新数据集时发生错误", "deleteSuccess": "删除成功", "fileDatasetDeleted": "文件数据集已成功删除", "databaseDatasetDeleted": "数据库数据集已成功删除", "deleteFailed": "删除失败", "deleteFileError": "删除文件数据集时发生错误", "deleteDatabaseError": "删除数据库数据集时发生错误", "buildSuccess": "构建中", "buildStarted": "数据集已开始构建", "buildFailed": "构建失败", "buildError": "构建数据集时发生错误"}, "card": {"edit": "编辑", "delete": "删除", "build": "构建", "building": "构建中", "description": "描述:", "database": "数据库:", "currentVersion": "当前数据版本:", "buildingVersion": "构建中版本:", "createTime": "创建时间:", "creator": "创建人:", "none": "无", "noPermissionDelete": "您没有权限删除"}, "delete": {"title": "确认删除", "description": "如果数据集已绑定Agent，对应的绑定关系也会被删除，您确定要删除 \"{name}\" 吗？此操作无法撤销。"}, "fileDetail": {"title": "文件详情", "notFound": "未找到指定的文件", "loading": "加载中...", "backToDatasets": "数据集列表", "metadataConfig": "元数据配置", "fileName": "文件名", "fileSize": "文件大小", "status": {"parseSuccess": "解析成功", "parseFailed": "解析失败", "parsing": "解析中", "unparsed": "未解析", "cancelling": "取消中", "cancelled": "已取消", "unknown": "未知状态"}, "actions": "操作", "messages": {"parseQueueSuccess": "个文件已进入解析队列", "stopParseSuccess": "个文件正在取消解析中", "success": "成功", "error": "错误"}, "progress": "进度", "updateTime": "修改时间", "addFiles": "添加文件", "batchDelete": "批量删除", "batchStart": "批量开始", "batchStop": "批量停止", "batchReset": "批量重置", "selectedFilesCount": "已选择 {count} 项", "deleteFile": "删除文件", "resetParse": "重置解析", "startParse": "开始解析", "stopParse": "停止解析", "configMetadata": "配置元数据", "noPermissionDelete": "您没有权限删除", "noPermissionConfig": "您没有权限配置", "file": "File", "worksheets": "工作表", "worksheet": "工作表", "subtable": "子表", "buildDataset": "构建数据集", "saveConfig": "保存配置", "hasUnsavedChanges": "有未保存的更改", "configNote": "配置过程中数据缓存在本地, 请勿刷新页面", "createdBy": "创建人", "createTime2": "创建时间", "updatedBy": "修改人"}, "deleteDialog": {"title": "确认删除", "singleFile": "您确定要删除文件 \"{fileName}\" 吗？", "multipleFiles": "您确定要删除选中的 {count} 个文件吗？", "description": "此操作无法撤销。"}, "resetDialog": {"title": "确认重置解析", "singleFile": "您确定要重置文件 \"{fileName}\" 的解析吗？", "multipleFiles": "您确定要重置选中的 {count} 个文件的解析吗？", "description": "重置后需要重新解析。"}}, "password": {"changeTitle": "修改帐户密码", "changeDescription": "请输入新密码以完成修改", "newPassword": "新密码", "confirmPassword": "确认新密码", "newPasswordPlaceholder": "输入新密码", "confirmPasswordPlaceholder": "再次输入新密码", "cancel": "取消", "confirm": "确认修改", "submitting": "提交中...", "changeFailed": "修改密码失败，请重试", "changeSuccess": "密码修改成功！对话框将自动关闭...", "validation": {"newPasswordRequired": "新密码至少需要6个字符", "confirmPasswordRequired": "确认密码至少需要6个字符", "passwordNotMatch": "新密码与确认密码不匹配"}, "title": "数据集管理", "fileDataset": "文件数据集", "databaseDataset": "数据库数据集", "addFileDataset": "添加文件数据集", "addDatabaseDataset": "添加数据库数据集", "loading": "加载中...", "loadError": "加载数据集列表失败: {error}", "retry": "重试", "unknownError": "未知错误", "create": "创建数据集", "saveChanges": "保存修改", "emptyMessage": "暂无数据集，点击上方按钮创建第一个数据集", "form": {"name": "数据集名称", "nameRequired": "数据集名称不能为空", "description": "数据集描述", "descriptionRequired": "数据集描述不能为空", "username": "用户名", "usernameRequired": "用户名不能为空", "password": "密码", "passwordRequired": "密码不能为空", "database": "数据库名称", "databaseRequired": "数据库名称不能为空", "testConnection": "测试连接", "connectionSuccess": "连接成功！数据库可以正常访问。", "connectionError": "连接发生错误，请稍后重试。", "testing": "测试中...", "mustTestFirst": "请先测试连接成功后再保存", "create": "创建数据集", "update": "更新数据集"}, "messages": {"createSuccess": "创建成功", "datasetCreated": "数据集已创建", "createFailed": "创建失败", "createError": "创建数据集时发生错误", "updateSuccess": "更新成功", "datasetUpdated": "数据集已更新", "updateFailed": "更新失败", "updateError": "更新数据集时发生错误", "deleteSuccess": "删除成功", "fileDatasetDeleted": "文件数据集已成功删除", "databaseDatasetDeleted": "数据库数据集已成功删除", "deleteFailed": "删除失败", "deleteFileError": "删除文件数据集时发生错误", "deleteDatabaseError": "删除数据库数据集时发生错误", "buildSuccess": "构建中", "buildStarted": "数据集已开始构建", "buildFailed": "构建失败", "buildError": "构建数据集时发生错误"}, "card": {"edit": "编辑", "delete": "删除", "description": "描述:", "createTime": "创建时间:", "creator": "创建人:", "none": "无"}, "delete": {"title": "确认删除", "description": "如果数据集已绑定Agent，对应的绑定关系也会被删除，您确定要删除 \"{name}\" 吗？此操作无法撤销。"}, "fileDetail": {"title": "文件详情", "notFound": "未找到指定的文件", "loading": "加载中...", "backToDatasets": "数据集列表", "metadataConfig": "元数据配置", "fileName": "文件名", "fileSize": "文件大小", "status": "状态", "progress": "进度", "updateTime": "修改时间", "actions": "操作", "addFiles": "添加文件", "batchDelete": "批量删除", "batchStart": "批量开始", "batchStop": "批量停止", "batchReset": "批量重置", "selectedFilesCount": "已选择 {count} 项", "deleteFile": "删除文件", "resetParse": "重置解析", "startParse": "开始解析", "stopParse": "停止解析", "configMetadata": "配置元数据", "noPermissionDelete": "您没有权限删除", "noPermissionConfig": "您没有权限配置", "file": "File", "worksheets": "工作表", "worksheet": "工作表", "subtable": "子表", "buildDataset": "构建数据集", "saveConfig": "保存配置", "hasUnsavedChanges": "有未保存的更改"}, "deleteDialog": {"title": "确认删除", "singleFile": "您确定要删除文件 \"{fileName}\" 吗？", "multipleFiles": "您确定要删除选中的 {count} 个文件吗？", "description": "此操作无法撤销。"}, "resetDialog": {"title": "确认重置解析", "singleFile": "您确定要重置文件 \"{fileName}\" 的解析吗？", "multipleFiles": "您确定要重置选中的 {count} 个文件的解析吗？", "description": "重置后需要重新解析。"}}, "home": {"welcomeTitle": "欢迎使用 {siteName}", "welcomeBack": "欢迎回来", "description": "是一项以个人和企业数据集为中心的人工智能服务，旨在释放数据的全部潜力。", "adminDescription": " 作为团队管理员，您可以管理团队成员和配置模型。", "userDescription": " 您可以访问团队共享的资源和创建个人资源。"}, "fileManagement": {"title": "文件管理", "fileList": "文件列表", "filesCount": "{count} 个文件", "searchPlaceholder": "搜索文件...", "selectedFiles": "已选择 {count} 个文件", "batchParse": "批量解析", "noFilesFound": "未找到匹配的文件", "fileName": "文件名称", "parseStatus": "解析状态", "actions": "操作", "status": {"success": "解析成功", "failed": "解析失败", "parsing": "解析中", "none": "未解析"}, "parseButton": {"parse": "解析", "reparse": "重新解析"}}, "forms": {"placeholders": {"username": "输入用户名", "password": "输入密码", "datasetName": "输入数据集名称", "datasetDescription": "输入数据集描述", "database": "输入数据库", "knowledgeBaseName": "输入知识库名称", "knowledgeBaseDescription": "输入知识库描述", "description": "请输入描述...", "search": "搜索...", "folderName": "请输入文件夹名称", "searchMembers": "搜索成员...", "searchFiles": "搜索文件..."}, "selectOptions": {"pdfParser": "选择PDF解析器", "sliceMethod": "选择切片方法", "defaultSliceMethod": "选择文档默认切片方法"}}, "sliceMethod": {"naive": {"description": "默认切片大小为512，用户可每个文件进行单独设置"}}, "sidebar": {"projects": {"files": "文件", "knowledgeBase": "知识库", "datasets": "数据集", "teamManagement": "团队管理", "modelConfig": "模型配置"}, "agent": {"addAgent": "添加Agent", "myAgent": "我的Agent", "teamAgent": "团队Agent", "createSuccess": "Agent 创建成功"}, "user": {"defaultUser": "用户", "changePassword": "修改密码", "logout": "退出登录"}, "team": {"admin": "管理员", "member": "成员", "personalSpace": "个人空间", "enterpriseSpace": "企业空间", "personalSpaceSuffix": "的个人空间", "spaceSuffix": "的空间"}}, "files": {"title": "文件管理", "actions": {"uploadFile": "上传文件", "newItem": "新增", "createFolder": "新建文件夹", "renameFile": "重命名文件", "deleteFile": "删除文件", "moveFile": "移动文件", "noPermissionDelete": "您没有权限删除", "selectFiles": "选择文件", "clearFiles": "清空", "startUpload": "开始上传", "retryUpload": "重新上传", "retry": "重试", "confirm": "确认", "cancel": "取消"}, "status": {"uploadSuccess": "上传成功", "uploadFailed": "上传失败", "uploading": "上传中", "waitingUpload": "等待上传", "userCancel": "用户取消"}, "messages": {"createFolderSuccess": "创建成功", "createFolderSuccessDesc": "文件夹 {folderName} 已创建", "createFolderFailed": "创建失败", "createFolderFailedDesc": "创建文件夹时发生错误", "renameFolderSuccess": "重命名成功", "renameFolderSuccessDesc": "文件已重命名为 {newName}", "renameFolderFailed": "重命名失败", "renameFolderFailedDesc": "重命名文件时发生错误", "deleteSuccess": "删除成功", "deleteSuccessDesc": "文件已成功删除", "deleteFailed": "删除失败", "deleteFailedDesc": "删除文件时发生错误", "uploadSuccessCount": "上传{count}个文件成功", "filesSuccessCount": "{count} 个文件上传成功"}, "table": {"name": "名称", "type": "类型", "size": "大小", "linkedCount": "被链接数", "createTime": "创建时间", "creator": "创建人", "updateTime": "修改时间", "updater": "修改人", "actions": "操作"}, "upload": {"title": "上传文件", "supportedFormats": "支持格式如下： {formats}", "fileLimits": "最多可选择 {maxCount} 个文件, excel, csv 单个文件最大{tableLimit}MB, 其他文件单个文件最大 {fileLimit}MB", "selectedFiles": "已选择的文件 ({count})", "uploadProgress": "上传中 {percent}%"}, "dialog": {"deleteTitle": "删除文件", "deleteConfirm": "确认删除", "deleteCancel": "取消", "moveTitle": "移动{type}", "renameTitle": "重命名{type}", "renamePlaceholder": "请输入新的{type}名称", "folderNameRequired": "文件夹名称不能为空", "nameRequired": "名称不能为空"}, "types": {"file": "文件", "folder": "文件夹"}}}
"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@ragtop-web/ui/components/accordion";
import { Avatar, AvatarFallback } from "@ragtop-web/ui/components/avatar";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { cn } from "@ragtop-web/ui/lib/utils";
import {
  AlertCircle,
  Bot,
  Check,
  ChevronDown,
  ChevronUp,
  Copy,
  Loader2,
  PersonStanding,
} from "lucide-react";
import { useEffect, useState } from "react";

import { FileDisplay } from "@/components/file-display";
import { MarkdownRenderer } from "@/components/markdown-renderer";
import { MessageStep } from "@/hooks/use-chat-stream";
import { replaceFilesOriginPlaceholder } from "@/lib/chat-utils";
import "@/styles/accordion-timeline.css";
import { Popover, PopoverContent, PopoverTrigger } from "@ragtop-web/ui/components/popover";
import { AnimatePresence, motion } from "motion/react";
import { copyToClipboard } from "../../lib/utils";

export type MessageRole = "user" | "assistant";

// 用于用户消息的props
interface UserMessageProps {
  message: any;
  role: string;
  className?: string;
  index?: number;
  agentName?: string;
}

// 用于AI助手消息的props
interface AssistantMessageProps {
  message: any;
  role: string;
  className?: string;
  index?: number;
  agentName?: string;
}

export type ChatMessageProps = UserMessageProps | AssistantMessageProps;

// 处理单个步骤的组件
function StepContent({ step }: { step: MessageStep }) {
  if (step.subSteps && step.subSteps.length > 0) {
    return (
      <div>
        {step.subSteps.map((subStep) => {
          return <StepContent key={subStep.id} step={subStep} />;
        })}
      </div>
    );
  }

  if (!step || !step.messages) {
    return <></>;
  }

  return (
    <div>
      {step.messages.map((msg, index) => {
        if (!msg.content) {
          return <div key={"empty" + index} />;
        } else {
          return (
            <div key={index + msg.id} className="prose dark:prose-invert max-w-none px-2">
              <MarkdownRenderer
                id={index + "_" + msg.id + "markdown"}
                key={index + "_" + msg.id + "markdown"}
                content={msg.content}
                annotations={msg.annotations}
              />
            </div>
          );
        }
      })}
      {step.files && step.files.length > 0 && (
        <div className="mt-2">
          <FileDisplay
            files={step.files.map((file) => {
              const fileUrl = replaceFilesOriginPlaceholder(file.uri);
              return {
                title: file.name,
                type: file.mime_type,
                uri: fileUrl,
                viewMode: file.view_mode,
                content: file.content,
              };
            })}
          />
        </div>
      )}
    </div>
  );
}

// 新增思考中卡片组件
function ThinkingCard({
  thinking,
}: {
  thinking: {
    status: string;
    content: string;
    startTime: number;
    endTime: number;
  };
}) {
  const [open, setOpen] = useState(thinking.status === "started");
  const { startTime, endTime, status, content } = thinking;

  useEffect(() => {
    if (status === "finished") {
      requestAnimationFrame(() => setOpen(false));
    }
    if (status === "started") {
      requestAnimationFrame(() => setOpen(true));
    }
  }, [status]);

  // 判断当前展示内容和动画
  let displayText = "";
  let textClass = "";
  if (!content) {
    // 没有内容，展示 is thinking，呼吸动画
    displayText = "思考中...";
    textClass = "animate-pulse";
  } else if (status === "started") {
    // 有内容但还在思考，展示 thinking，呼吸动画
    displayText = "思考中...";
    textClass = "animate-pulse";
  } else if (status === "finished") {
    // const seconds = endTime > startTime ? Math.round((endTime - startTime)) : undefined;
    // 完全返回，展示 thinking for x seconds，hover 变色动画
    displayText = "已完成思考";
    textClass = "transition-colors hover:text-gray-900 text-gray-600 dark:text-gray-200";
  }

  if (status == "finished" && !content) {
    return <></>;
  }

  return (
    <div className="relative w-full py-2 transition-all duration-200">
      <div
        className="group flex cursor-pointer items-center select-none"
        onClick={() => setOpen((v) => !v)}
      >
        <span className={`text-sm ${textClass}`}>{displayText}</span>
        <span className="ml-2">
          {open ? (
            <ChevronUp className="h-4 w-4" strokeWidth={1.5} />
          ) : (
            <ChevronDown className="h-4 w-4" strokeWidth={1.5} />
          )}
        </span>
      </div>

      <AnimatePresence initial={false}>
        {open && (
          <motion.div
            key="thinking-content"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            style={{ overflow: "hidden" }}
            className="mt-2 text-sm text-gray-600 dark:text-gray-300"
          >
            {content}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export function ChatMessage(props: ChatMessageProps) {
  const { role, className, message, index } = props;
  const { isComplete, thinking = {} } = message;
  const { toast } = useToast();

  // 新 step 自动展开+可手动收起
  const [openIds, setOpenIds] = useState<string[]>([]);

  useEffect(() => {
    if (props.message!.steps && props.message.msgAutoOpen) {
      const stepIds = props.message!.steps.map((step: any) => step.id);
      setOpenIds((prev) => Array.from(new Set([...prev, ...stepIds])));
    } else {
      setOpenIds([]);
    }
  }, [message?.steps?.length]);

  return (
    <div className={cn("py-2", className)}>
      {/* 用户消息 */}
      {role === "user" ? (
        <div className="group flex flex-col items-end">
          <div className="flex items-center">
            <div className="mr-2 hidden text-xs text-gray-500 group-hover:block">
              {props.message?.createTime}
            </div>
            <Avatar className="h-6 w-6">
              <AvatarFallback className="bg-transparent">
                <PersonStanding className="h-6 w-6" />
              </AvatarFallback>
            </Avatar>
          </div>
          <div className="max-w-[50%] py-2">
            <div className="text-sm">{props.message.content}</div>
          </div>
        </div>
      ) : (
        /* Agent消息显示在左侧 */
        <div className="group flex flex-col items-start">
          <div className="flex items-center">
            <Avatar className="mr-2 h-6 w-6">
              <AvatarFallback className="bg-transparent">
                <Bot className="h-6 w-6" />
              </AvatarFallback>
            </Avatar>
            <span className="font-medium">{props.agentName || "AI Assistant"}</span>
            <div className="ml-2 hidden text-xs text-gray-500 group-hover:block">
              {props.message?.createTime}
            </div>
          </div>

          <div className="inline-block w-[700px]">
            {/* 思考中卡片，step之前展示 */}
            {props.message?.thinking && <ThinkingCard thinking={props.message.thinking} />}

            {/* step 展示 */}
            {props.message && props.message.steps && props.message.steps.length > 0 && (
              <>
                {!props.message.isStepMsg ? (
                  <div
                    className={
                      (index === 0 ? "" : "border border-gray-200 ") + "mt-1 rounded-lg p-3"
                    }
                  >
                    <StepContent step={props.message.steps[0]!} />
                  </div>
                ) : (
                  <Accordion
                    type="multiple"
                    value={openIds}
                    onValueChange={setOpenIds}
                    className="mt-2 w-[700px]"
                  >
                    {props.message!.steps.map((step: any, idx: number) => (
                      <AccordionItem
                        key={step.id}
                        value={step.id}
                        className="step-item relative mb-4 rounded-md border dark:border-gray-700"
                      >
                        <AccordionTrigger className="p-2 hover:no-underline">
                          <div className="flex w-full items-center gap-2 text-sm">
                            <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-black text-xs font-bold text-white dark:bg-white dark:text-black">
                              {idx + 1}
                            </span>
                            <span className="flex-1 self-center text-left break-words whitespace-pre-line">
                              {step.name}
                            </span>
                            <div className="ml-auto flex items-center gap-2 self-center whitespace-nowrap">
                              {step.status === "started" && (
                                <Loader2 className="text-muted-foreground h-4 w-4 animate-spin" />
                              )}
                              {step.status === "finished" && (
                                <Check className="h-4 w-4 text-green-500" />
                              )}
                              {step.status === "error" && (
                                <Popover>
                                  <PopoverTrigger asChild onClick={(e) => e.stopPropagation()}>
                                    <AlertCircle className="h-4 w-4 cursor-pointer text-red-500" />
                                  </PopoverTrigger>
                                  <PopoverContent
                                    side="top"
                                    className="relative w-72 pt-8"
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    <button
                                      className="absolute top-2 right-2 z-10 rounded-sm px-2 py-1 transition hover:bg-zinc-200"
                                      aria-label="复制错误信息"
                                      onClick={async (event) => {
                                        event.stopPropagation();
                                        await copyToClipboard(step.error || "未知错误");
                                        toast({
                                          title: "复制成功",
                                        });
                                      }}
                                      type="button"
                                    >
                                      <Copy className="h-4 w-4" />
                                    </button>
                                    <div className="flex max-h-48 flex-col gap-2 overflow-y-auto">
                                      <div className="text-xs break-all whitespace-pre-line text-red-400">
                                        {step.error || "未知错误"}
                                      </div>
                                    </div>
                                  </PopoverContent>
                                </Popover>
                              )}
                            </div>
                          </div>
                        </AccordionTrigger>
                        {/* 之所以要这份代码是因为 Accordion 展开的时候 content会有点高度，应该也可以使用css控制 todo */}
                        {(step.messages.length > 0 ||
                          step.files.length > 0 ||
                          (step.subSteps && step.subSteps?.length > 0)) && (
                          <AccordionContent className="px-3 pb-4">
                            <StepContent step={step} />
                          </AccordionContent>
                        )}
                      </AccordionItem>
                    ))}
                  </Accordion>
                )}
              </>
            )}

            {/* 错误消息 */}
            {props.message?.error && (
              <div className="mt-2 flex flex-row items-center gap-3 rounded-md border border-red-200 bg-red-50 p-2 dark:border-red-900 dark:bg-red-950">
                <span className="text-red-500">
                  <AlertCircle className="h-5 w-5" />
                </span>
                <span className="text-sm break-all text-red-700 dark:text-red-300">
                  回答出现了一点小问题，请您重新提问～
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {role == "assistant" && !isComplete && thinking.status == "finished" && (
        <div className="text-muted-foreground flex items-center space-x-2 text-sm">
          <Loader2 className="h-4 w-4 animate-spin" strokeWidth={1.5} />
          <span>正在生成回复...</span>
        </div>
      )}
    </div>
  );
}

// 用 memo 包裹 ChatMessage，只有 message 对象引用变化才会重新渲染
// export const MemoChatMessage = React.memo(ChatMessage, (prev, next) => prev.message === next.message);

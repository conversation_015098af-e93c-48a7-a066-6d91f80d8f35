"use client";

import { But<PERSON> } from "@ragtop-web/ui/components/button";
import { Textarea } from "@ragtop-web/ui/components/textarea";
import { cn } from "@ragtop-web/ui/lib/utils";
import { CirclePause, Send } from "lucide-react";
import { useState } from "react";

interface ChatInputProps {
  onSend: (message: string) => void;
  onCancel?: () => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  isStreaming: boolean;
}

export function ChatInput({
  onSend,
  onCancel,
  isStreaming,
  className,
  placeholder = "请输入您的问题......",
  disabled = false,
}: ChatInputProps) {
  const [message, setMessage] = useState("");
  const [composing, setComposing] = useState(false);
  const [cancelDisabled, setCancelDisabled] = useState(false);

  const handleCancel = async () => {
    if (cancelDisabled) return;
    setCancelDisabled(true);
    try {
      await onCancel?.();
      setCancelDisabled(false);
    } catch (error) {
      setCancelDisabled(false);
    }
  };

  const handleSend = () => {
    if (message.trim() && !disabled) {
      onSend(message);
      setMessage("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey && !disabled && !composing) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div
      className={cn(
        "flex items-center justify-center gap-2 rounded-lg border-2 border-gray-200 bg-white p-2 shadow-lg dark:border-gray-700 dark:bg-gray-800",
        className
      )}
    >
      <Textarea
        value={message}
        onCompositionStart={() => setComposing(true)}
        onCompositionEnd={() => setComposing(false)}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={disabled ? "正在处理中..." : placeholder}
        disabled={disabled}
        className="max-h-32 min-h-[40px] flex-1 resize-none border-0 bg-transparent py-0 leading-[40px] text-gray-900 shadow-none placeholder:text-gray-400 focus-visible:ring-0 dark:text-gray-100 dark:placeholder:text-gray-500"
        style={{ boxShadow: "none" }}
      />

      {isStreaming ? (
        <div
          onClick={handleCancel}
          className="flex h-8 w-8 items-center justify-center rounded-md bg-gray-200 text-gray-600 transition-colors"
          title="取消回答"
        >
          <CirclePause className="h-5 w-5" />
        </div>
      ) : (
        <Button
          type="submit"
          className={cn(
            "flex h-8 w-8 items-center justify-center rounded-md p-0",
            disabled || !message.trim() ? "cursor-not-allowed" : ""
          )}
          aria-label="Send"
          onClick={handleSend}
          disabled={disabled || !message.trim()}
        >
          <Send className="h-5 w-5" />
        </Button>
      )}
    </div>
  );
}

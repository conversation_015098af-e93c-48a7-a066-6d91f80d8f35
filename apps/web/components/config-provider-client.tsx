// app/provider/ConfigProviderClient.tsx
"use client";
import { createContext, useContext } from "react";

const ConfigContext = createContext<Record<string, string> | null>(null);

export default function ConfigProviderClient({
  config,
  children,
}: {
  config: Record<string, string>;
  children: React.ReactNode;
}) {
  return <ConfigContext.Provider value={config}>{children}</ConfigContext.Provider>;
}

export function useConfig() {
  const ctx = useContext(ConfigContext);
  if (!ctx) throw new Error("请将组件包装在 ConfigProvider 下");
  return ctx;
}

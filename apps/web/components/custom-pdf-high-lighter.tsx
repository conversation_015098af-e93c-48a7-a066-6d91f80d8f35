import { PdfHighlighter } from "react-pdf-highlighter";

type CustomHighlight = any;

class CustomPdfHighlighter extends PdfHighlighter<CustomHighlight> {
  constructor(props: React.ComponentProps<typeof PdfHighlighter>) {
    super(props);
  }

  findOrCreateHighlightLayer(page: number): Element | null {
    if (!this.viewer) {
      super.init();

      let highlightLayer: Element | null = null;

      setTimeout(() => {
        highlightLayer = super.findOrCreateHighlightLayer(page);
      }, 250);

      return highlightLayer;
    }

    return super.findOrCreateHighlightLayer(page);
  }
}

export default CustomPdfHighlighter;

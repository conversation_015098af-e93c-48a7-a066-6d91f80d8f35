"use client";

import { <PERSON><PERSON>, DialogContent, DialogTrigger } from "@ragtop-web/ui/components/dialog";
import { FileText, ZoomIn } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

interface ImagePreviewProps {
  imageUri?: string;
  alt?: string;
  className?: string;
  onImageClick?: () => void;
  fallbackIcon?: React.ReactNode;
  showZoomIcon?: boolean;
  width?: number;
  height?: number;
}

export function ImagePreview({
  imageUri,
  alt = "预览图片",
  className = "h-12 w-12",
  onImageClick,
  fallbackIcon,
  showZoomIcon = true,
  width = 48,
  height = 48,
}: ImagePreviewProps) {
  const [imageError, setImageError] = useState(false);

  if (!imageUri || imageError) {
    return (
      <div
        className={`flex items-center justify-center rounded border border-dashed border-gray-300 ${className}`}
      >
        {fallbackIcon || <FileText className="h-6 w-6 text-gray-400" />}
      </div>
    );
  }

  return (
    <div className="group relative cursor-pointer">
      <Dialog>
        <DialogTrigger asChild>
          <div className="relative">
            <Image
              src={imageUri}
              alt={alt}
              width={width}
              height={height}
              className={`bg-muted rounded border object-cover transition-all duration-200 group-hover:opacity-80 ${className}`}
              onClick={onImageClick}
              onError={() => setImageError(true)}
              unoptimized // 用于处理可能来自外部源的图片
              priority={false}
            />
            {showZoomIcon && (
              <div className="absolute inset-0 flex items-center justify-center rounded bg-black/20 opacity-0 transition-opacity duration-200 group-hover:opacity-100">
                <ZoomIn className="h-4 w-4 text-white" />
              </div>
            )}
          </div>
        </DialogTrigger>
        <DialogContent className="max-h-[90vh] max-w-4xl p-0">
          <div className="relative flex items-center justify-center">
            <Image
              src={imageUri}
              alt={`${alt} - 放大预览`}
              width={800}
              height={600}
              className="h-auto max-h-[85vh] w-auto max-w-full object-contain"
              unoptimized // 用于处理可能来自外部源的图片
              priority
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

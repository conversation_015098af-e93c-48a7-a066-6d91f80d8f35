"use client";

import { useExistingModels, useLLMSettings, useQueryLLMSettings } from "@/service/model-service";
import { zodResolver } from "@hookform/resolvers/zod";
import { Badge } from "@ragtop-web/ui/components/badge";
import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ragtop-web/ui/components/select";
import { isEmpty } from "lodash-es";
import { useTranslations } from "next-intl";
import { useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

// 定义模式创建函数，以便访问翻译
const createFormSchema = (t: (key: string) => string) =>
  z.object({
    chat_model: z.string().min(1, t("validation.selectModel")),
    embd_model: z.string().min(1, t("validation.selectModel")),
    rerank_model: z.string().min(1, t("validation.selectModel")),
    nl2sql_model: z.string().min(1, t("validation.selectModel")),
    nl2python_model: z.string().min(1, t("validation.selectModel")),
  });

type FormValues = z.infer<ReturnType<typeof createFormSchema>>;

interface PasswordChangeDialogProps {
  open: boolean;
  onClose: (success?: boolean) => void;
}

/**
 * 密码修改对话框组件
 */
export function ModelChangeDialog({ open, onClose }: PasswordChangeDialogProps) {
  const { data: configuredModels } = useExistingModels(open);
  const { data: llmSettings } = useQueryLLMSettings(open);
  const settingMutate = useLLMSettings();
  const t = useTranslations("model");

  const notClose = useMemo(() => isEmpty(llmSettings) && llmSettings !== undefined, [llmSettings]);

  const chatOption = configuredModels?.filter((option) => option.model_purpose === "CHAT");
  const embdOption = configuredModels?.filter((option) => option.model_purpose === "EMBEDDING");
  const rerankOption = configuredModels?.filter((option) => option.model_purpose === "RERANK");
  const nl2sqlOption = configuredModels?.filter((option) => option.model_purpose === "NL2SQL");
  const nl2codeOption = configuredModels?.filter((option) => option.model_purpose === "NL2PYTHON");

  // 创建表单验证模式
  const formSchema = useMemo(() => createFormSchema(t), [t]);

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      chat_model: llmSettings?.chat_model || "",
      embd_model: llmSettings?.embd_model || "",
      rerank_model: llmSettings?.rerank_model || "",
      nl2sql_model: llmSettings?.nl2sql_model || "",
      nl2python_model: llmSettings?.nl2python_model || "",
    },
  });

  // 处理修改
  const handleSubmit = (values: FormValues) => {
    settingMutate.mutate(values, {
      onSuccess: () => {
        onClose(true);
      },
    });
  };

  useEffect(() => {
    if (llmSettings) {
      form.reset({
        chat_model: llmSettings.chat_model,
        embd_model: llmSettings.embd_model,
        rerank_model: llmSettings?.rerank_model,
        nl2sql_model: llmSettings?.nl2sql_model,
        nl2python_model: llmSettings?.nl2python_model,
      });
    }
  }, [llmSettings]);

  return (
    <Dialog open={open} onOpenChange={() => onClose(!notClose)}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>{t("description")}</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="chat_model"
              rules={{ required: t("validation.selectChatModel") }}
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("chatModel")}</RequiredFormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-[622px]">
                        <SelectValue placeholder={t("placeholder")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {chatOption?.map((option) => (
                        <SelectItem key={option.llm_id} value={option.llm_id}>
                          <div className="flex items-center gap-2">
                            <span>{option.llm_alias || option.llm_name}</span>
                            {option.llm_alias && option.llm_name && (
                              <Badge variant="secondary" className="text-xs">
                                {option.llm_name}
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="embd_model"
              rules={{ required: t("validation.selectEmbeddingModel") }}
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("embeddingModel")}</RequiredFormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-[622px]">
                        <SelectValue placeholder={t("placeholder")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {embdOption?.map((option) => (
                        <SelectItem key={option.llm_id} value={option.llm_id}>
                          <div className="flex items-center gap-2">
                            <span>{option.llm_alias || option.llm_name}</span>
                            {option.llm_alias && option.llm_name && (
                              <Badge variant="secondary" className="text-xs">
                                {option.llm_name}
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="rerank_model"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("rerankModel")}</RequiredFormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-[622px]">
                        <SelectValue placeholder={t("placeholder")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {rerankOption?.map((option) => (
                        <SelectItem key={option.llm_id} value={option.llm_id}>
                          <div className="flex items-center gap-2">
                            <span>{option.llm_alias || option.llm_name}</span>
                            {option.llm_alias && option.llm_name && (
                              <Badge variant="secondary" className="text-xs">
                                {option.llm_name}
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="nl2sql_model"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("nl2sqlModel")}</RequiredFormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-[622px]">
                        <SelectValue placeholder={t("placeholder")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {nl2sqlOption?.map((option) => (
                        <SelectItem key={option.llm_id} value={option.llm_id}>
                          <div className="flex items-center gap-2">
                            <span>{option.llm_alias || option.llm_name}</span>
                            {option.llm_alias && option.llm_name && (
                              <Badge variant="secondary" className="text-xs">
                                {option.llm_name}
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="nl2python_model"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("nl2pythonModel")}</RequiredFormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-[622px]">
                        <SelectValue placeholder={t("placeholder")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {nl2codeOption?.map((option) => (
                        <SelectItem key={option.llm_id} value={option.llm_id}>
                          <div className="flex items-center gap-2">
                            <span>{option.llm_alias || option.llm_name}</span>
                            {option.llm_alias && option.llm_name && (
                              <Badge variant="secondary" className="text-xs">
                                {option.llm_name}
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              {notClose && (
                <Button type="button" variant="outline" onClick={() => onClose(!notClose)}>
                  {t("cancel")}
                </Button>
              )}
              <Button type="submit">
                {settingMutate.isPending ? (
                  <span className="flex items-center gap-2">
                    <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    {t("submitting")}
                  </span>
                ) : (
                  t("confirm")
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

"use client";

import { usePasswordModify } from "@/service/auth-service";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@ragtop-web/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import { useTranslations } from "next-intl";
import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

// 表单验证模式创建函数
const createFormSchema = (t: (key: string) => string) =>
  z
    .object({
      newPassword: z.string().min(6, t("validation.newPasswordRequired")),
      confirmPassword: z.string().min(6, t("validation.confirmPasswordRequired")),
    })
    .refine((data) => data.newPassword === data.confirmPassword, {
      message: t("validation.passwordNotMatch"),
      path: ["confirmPassword"],
    });

type FormValues = z.infer<ReturnType<typeof createFormSchema>>;

interface PasswordChangeDialogProps {
  open: boolean;
  onClose: () => void;
}

/**
 * 密码修改对话框组件
 */
export function PasswordChangeDialog({ open, onClose }: PasswordChangeDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");
  const passwordModifyMutation = usePasswordModify();
  const t = useTranslations("password");

  // 创建表单验证模式
  const formSchema = useMemo(() => createFormSchema(t), [t]);

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    },
  });

  // 处理密码修改
  const handleSubmit = (values: FormValues) => {
    setIsLoading(true);
    setError("");

    passwordModifyMutation.mutate(
      { new_password: values.newPassword },
      {
        onSuccess: () => {
          setSuccess(true);
          form.reset();
          setTimeout(() => {
            onClose();
            setSuccess(false);
          }, 3000);
        },
        onError: (error) => {
          setError(t("changeFailed"));
          setIsLoading(false);
        },
      }
    );
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t("changeTitle")}</DialogTitle>
          <DialogDescription>{t("changeDescription")}</DialogDescription>
        </DialogHeader>

        {error && (
          <div className="bg-destructive/15 text-destructive rounded-md p-3 text-sm">{error}</div>
        )}

        {success && (
          <div className="rounded-md bg-green-500/15 p-3 text-sm text-green-500">
            {t("changeSuccess")}
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("newPassword")}</RequiredFormLabel>
                  <FormControl>
                    <Input type="password" placeholder={t("newPasswordPlaceholder")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("confirmPassword")}</RequiredFormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder={t("confirmPasswordPlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                {t("cancel")}
              </Button>
              <Button type="submit" disabled={isLoading || success}>
                {isLoading ? (
                  <span className="flex items-center gap-2">
                    <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    {t("submitting")}
                  </span>
                ) : (
                  t("confirm")
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

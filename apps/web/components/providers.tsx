"use client";

import { QueryProvider } from "@ragtop-web/ui/components/query-provider";
import { Provider as <PERSON><PERSON><PERSON>rovider } from "jotai";
import { NextIntlClientProvider } from "next-intl";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import * as React from "react";

/**
 * 应用程序提供者组件
 *
 * 包含主题提供者、状态管理提供者、查询提供者和国际化提供者
 */
export function Providers({
  children,
  locale,
  messages,
}: {
  children: React.ReactNode;
  locale: string;
  messages: any;
}) {
  return (
    <NextIntlClientProvider locale={locale} messages={messages}>
      <JotaiProvider>
        <QueryProvider>
          <NextThemesProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
            enableColorScheme
          >
            {children}
          </NextThemesProvider>
        </QueryProvider>
      </JotaiProvider>
    </NextIntlClientProvider>
  );
}

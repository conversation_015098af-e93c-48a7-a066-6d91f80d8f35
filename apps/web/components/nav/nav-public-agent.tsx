"use client";
import { Scope, useAgentsInfinite } from "@/service/agent-service";
import { useTranslations } from "next-intl";

import { NavAgent } from "./nav-agent";

export function NavPublicAgent() {
  const {
    data,
    isLoading,
    hasMore,
    isInitialLoading,
    isFetchingNextPage,
    error,
    fetchNextPage,
    reload,
  } = useAgentsInfinite(Scope.TeamPublic, 10, true);

  const t = useTranslations("sidebar.agent");

  return (
    <NavAgent
      data={data}
      isLoading={isLoading}
      hasMore={hasMore}
      isInitialLoading={isInitialLoading}
      isFetchingNextPage={isFetchingNextPage}
      error={error}
      fetchNextPage={fetchNextPage}
      reload={reload}
      title={t("teamAgent")}
    />
  );
}

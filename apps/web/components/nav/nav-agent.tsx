"use client";

import { Agents } from "@/service/agent-service";
import { InfiniteScrollList } from "@ragtop-web/ui/components/infinite-scroll-list";
import { SidebarGroup, SidebarGroupLabel, SidebarMenu } from "@ragtop-web/ui/components/sidebar";
import { NavSessionItem } from "./nav-session-item";

export function NavAgent({
  title,
  data,
  isLoading,
  hasMore,
  isInitialLoading,
  isFetchingNextPage,
  error,
  fetchNextPage,
  reload,
}: {
  title: string;
  data: Agents[];
  isLoading: boolean;
  hasMore: boolean;
  isInitialLoading: boolean;
  isFetchingNextPage: boolean;
  error: string | null;
  fetchNextPage: () => void;
  reload: () => void;
}) {
  return (
    <SidebarGroup>
      <SidebarGroupLabel className="pl-1">{title}</SidebarGroupLabel>
      <SidebarMenu>
        <InfiniteScrollList
          data={data}
          isLoading={isLoading}
          hasMore={hasMore}
          isInitialLoading={isInitialLoading}
          isFetchingNextPage={isFetchingNextPage}
          error={error}
          fetchNextPage={fetchNextPage}
          reload={reload}
          renderItem={(item) => <NavSessionItem key={item.id} agent={item} />}
          renderLoadDone={() => <></>}
          renderEmpty={() => <></>}
        />
      </SidebarMenu>
    </SidebarGroup>
  );
}

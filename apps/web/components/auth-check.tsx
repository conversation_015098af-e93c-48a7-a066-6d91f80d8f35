"use client";

import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { isAuthenticated } from "@/service/auth-service";

/**
 * 身份验证检查组件
 *
 * 检查用户是否已登录，如果未登录则重定向到登录页面
 * 同时根据用户角色限制对某些页面的访问
 */
export function AuthCheck({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 检查是否已登录（使用access_token）
    const isLoggedIn = isAuthenticated();

    // 如果未登录且不在登录页面，则重定向到登录页面
    if (!isLoggedIn && pathname !== "/login") {
      router.push("/login");
      return;
    }

    setIsLoading(false);
  }, [router, pathname]);

  // 如果正在加载，则显示加载状态
  if (isLoading && pathname !== "/login") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-current border-t-transparent"></div>
          <p className="text-muted-foreground text-sm">正在验证身份...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

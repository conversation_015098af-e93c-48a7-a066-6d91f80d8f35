"use client";

import { BookOpenText, Bot, Database, FileText, Plus, UserRound } from "lucide-react";
import { useTranslations } from "next-intl";
import * as React from "react";
import { useEffect, useState } from "react";

import { AgentDialog } from "@/components/agent/agent-dialog";
import { NavProjects } from "@/components/nav/nav-projects";
import { NavUser } from "@/components/nav/nav-user";
import { TeamSwitcher } from "@/components/nav/team-switcher";
import { useCurrentUser, useIsTeamAdmin } from "@/lib/user-role";
import { useCreateAgent } from "@/service/agent-service";
import { useListParseModels } from "@/service/knowledge-base-doc-service";
import { useQueryLLMSettings } from "@/service/model-service";
import { TeamType } from "@/service/team-service";
import { currentTeamAtom, getTeamId } from "@/store/team-store";
import { But<PERSON> } from "@ragtop-web/ui/components/button";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@ragtop-web/ui/components/sidebar";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { useAtomValue } from "jotai";
import { isEmpty } from "lodash-es";
import { ModelChangeDialog } from "./model-change-dialog";
import { NavPersonalAgent } from "./nav/nav-personal-agent";
import { NavPublicAgent } from "./nav/nav-public-agent";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [isAgentFormOpen, setIsAgentFormOpen] = useState(false);
  const [isModelDialogOpen, setIsModelDialogOpen] = React.useState(false);
  const currentTeam = useAtomValue(currentTeamAtom);
  const teamId = getTeamId();
  // 使用Hook获取状态
  const isAdmin = useIsTeamAdmin();
  const userData = useCurrentUser();
  const createAgent = useCreateAgent();
  const isPersonal = currentTeam?.type === TeamType.Personal;
  useListParseModels(true);
  const { data: llmSettings } = useQueryLLMSettings(isAdmin);
  const { toast } = useToast();
  const t = useTranslations("sidebar");
  // 处理添加 Agent
  const handleAddAgent = async (data: any) => {
    await createAgent.mutateAsync(data, {
      onSuccess: () => {
        toast({
          title: t("agent.createSuccess"),
          description: t("agent.createSuccess"),
        });
      },
    });
  };

  // 处理模版
  const handleOpenModelDialog = () => {
    setIsModelDialogOpen(true);
  };

  const handleCloseModelDialog = (success: boolean | undefined) => {
    // 没有设置llmsettings不让关闭
    if (!success) {
      return;
    }
    setIsModelDialogOpen(false);
  };

  const allProjects = [
    {
      name: t("projects.files"),
      url: "/files",
      icon: FileText,
    },
    {
      name: t("projects.knowledgeBase"),
      url: "/knowledge-base",
      icon: BookOpenText,
    },

    {
      name: t("projects.datasets"),
      url: "/datasets",
      icon: Database,
    },
    ...(!isPersonal
      ? [
          {
            name: t("projects.teamManagement"),
            url: "/users",
            icon: UserRound,
          },
        ]
      : []),
    {
      name: t("projects.modelConfig"),
      icon: Bot,
      onClick: handleOpenModelDialog,
    },
  ];

  useEffect(() => {
    if (isEmpty(llmSettings) && llmSettings !== undefined) {
      handleOpenModelDialog();
    }
  }, [llmSettings]);

  return (
    <>
      <Sidebar collapsible="icon" variant="inset" {...props}>
        <SidebarHeader>
          <TeamSwitcher />
          <Button onClick={() => setIsAgentFormOpen(true)}>
            <Plus />
            <span className="group-data-[collapsible=icon]:hidden">{t("agent.addAgent")}</span>
          </Button>
        </SidebarHeader>
        <SidebarContent className="no-scrollbar gap-0">
          {isAdmin && teamId && <NavProjects projects={allProjects} />}
          {teamId && <NavPersonalAgent />}
          {!isPersonal && teamId && <NavPublicAgent />}
        </SidebarContent>
        <SidebarFooter>
          <NavUser
            user={
              userData
                ? {
                    name: userData.username || t("user.defaultUser"),
                    email: userData.username || "",
                  }
                : {
                    name: t("user.defaultUser"),
                    email: "",
                  }
            }
          />
        </SidebarFooter>
        <SidebarRail />
        {isAgentFormOpen && (
          <AgentDialog
            open={isAgentFormOpen}
            onClose={() => setIsAgentFormOpen(false)}
            onSubmit={handleAddAgent}
          />
        )}
      </Sidebar>
      {/* Agent 添加对话框 */}

      {/* 模型配置对话框 */}
      {isModelDialogOpen && (
        <ModelChangeDialog open={isModelDialogOpen} onClose={handleCloseModelDialog} />
      )}
    </>
  );
}

"use client";

import { useSiteConfig } from "@/service/site-config-service";
import { siteConfigAtom } from "@/store/user-store";
import { useAtomValue } from "jotai";
import Head from "next/head";
import { usePathname } from "next/navigation";
import { useEffect } from "react";
import { useConfig } from "./config-provider-client";

export function SiteConfigProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  useSiteConfig();
  const siteConfig = useAtomValue(siteConfigAtom);
  const baseUrl = useConfig().WEB_API_URL || "";

  useEffect(() => {
    if (!siteConfig) {
      return;
    }
    // Update favicon when site config changes
    if (siteConfig?.favicon) {
      const favicon = document.querySelector("link[rel='icon']");
      if (favicon) {
        favicon.setAttribute("href", `${baseUrl}/_${siteConfig.favicon}`);
      } else {
        const link = document.createElement("link");
        link.rel = "icon";
        link.href = `${baseUrl}/_${siteConfig.favicon}`;
        document.head.appendChild(link);
      }
    }

    // Update document title when site config changes
    if (siteConfig?.name) {
      document.title = `${siteConfig.name} - 从知识和数据中快速获取洞察`;
      document
        .querySelector("meta[name='description']")
        ?.setAttribute(
          "content",
          `${siteConfig.name}是一项以个人和企业数据集为中心的人工智能服务，旨在释放数据的全部潜力。`
        );
    }
  }, [siteConfig, pathname]);

  return (
    <>
      <Head>
        {siteConfig?.favicon && <link rel="icon" href={`${baseUrl}/_${siteConfig.favicon}`} />}
      </Head>
      {children}
    </>
  );
}

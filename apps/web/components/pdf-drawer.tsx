"use client";

import { IReferenceChunk } from "@/service/session-service";
import "@/styles/pdf-highlighter.css";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@ragtop-web/ui/components/sheet";
import dynamic from "next/dynamic";

const PdfPreviewer = dynamic(() => import("./pdf-previewer"), {
  ssr: false,
  loading: () => <p>Loading PDF...</p>,
});

// import PdfHighlighter from './custom-pdf-high-lighter'

interface PdfPreviewerProps {
  url: string;
  fileName: string;
  trigger?: React.ReactNode;
  open?: boolean;
  chunk?: IReferenceChunk;
  onOpenChange?: (open: boolean) => void;
}
/**
 * PDF 预览组件
 *
 * 基于抽屉形式展示 PDF 预览
 */
export function PdfDrawer({
  url,
  fileName,
  trigger,
  open,
  chunk,
  onOpenChange,
}: PdfPreviewerProps) {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>{trigger}</SheetTrigger>
      <SheetContent side="right" className="w-full p-0 sm:max-w-2xl">
        <SheetHeader className="border-b px-6 py-4">
          <SheetTitle className="text-lg font-semibold">{fileName}</SheetTitle>
          <SheetDescription className="text-muted-foreground text-sm">
            PDF 文档预览
          </SheetDescription>
        </SheetHeader>
        <PdfPreviewer chunk={chunk} url={url} open={!!open} />
      </SheetContent>
    </Sheet>
  );
}

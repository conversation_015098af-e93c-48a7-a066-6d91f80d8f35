"use client";

import { Checkbox } from "@ragtop-web/ui/components/checkbox";
import { Label } from "@ragtop-web/ui/components/label";

import { useFilesetsInfinite } from "@/service/dataset-service";
import { InfiniteScrollList } from "@ragtop-web/ui/components/infinite-scroll-list";

interface DatasetSelectorProps {
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
  maxHeight?: string;
}

/**
 * 数据集选择器组件
 * 支持滚动加载和多选
 */
export function FilesetSelector({ selectedIds, onSelectionChange }: DatasetSelectorProps) {
  const {
    data,
    isLoading,
    hasMore,
    isInitialLoading,
    isFetchingNextPage,
    error,
    fetchNextPage,
    reload,
  } = useFilesetsInfinite(10);

  // 处理选择变化
  const handleSelectionChange = (datasetId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, datasetId]);
    } else {
      onSelectionChange(selectedIds.filter((id) => id !== datasetId));
    }
  };

  return (
    <div className="h-[200px] max-h-[200px] overflow-y-auto rounded-md border p-3">
      <InfiniteScrollList
        data={data}
        isLoading={isLoading}
        hasMore={hasMore}
        isInitialLoading={isInitialLoading}
        isFetchingNextPage={isFetchingNextPage}
        error={error}
        fetchNextPage={fetchNextPage}
        reload={reload}
        renderItem={(dataset) => (
          <div key={dataset.id} className="flex items-center space-x-2">
            <Checkbox
              id={`dataset-${dataset.id}`}
              checked={selectedIds.includes(dataset.id)}
              onCheckedChange={(checked) => handleSelectionChange(dataset.id, !!checked)}
            />
            <Label htmlFor={`dataset-${dataset.id}`} className="cursor-pointer text-sm">
              {dataset.name}
            </Label>
          </div>
        )}
      />
    </div>
  );
}

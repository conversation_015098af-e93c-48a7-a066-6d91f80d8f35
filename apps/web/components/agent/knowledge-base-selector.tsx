"use client";

import { Checkbox } from "@ragtop-web/ui/components/checkbox";
import { Label } from "@ragtop-web/ui/components/label";
import { InfiniteScrollList } from "@ragtop-web/ui/components/infinite-scroll-list";
import { useKnowledgeBasesInfinite } from "@/service/knowledge-base-service";

interface KnowledgeBaseSelectorProps {
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
  maxHeight?: string;
}

/**
 * 知识库选择器组件
 * 支持滚动加载和多选
 */
export function KnowledgeBaseSelector({
  selectedIds,
  onSelectionChange,
}: KnowledgeBaseSelectorProps) {
  const {
    data,
    isLoading,
    hasMore,
    isInitialLoading,
    isFetchingNextPage,
    error,
    fetchNextPage,
    reload,
  } = useKnowledgeBasesInfinite(10);

  // 处理选择变化
  const handleSelectionChange = (knowledgeBaseId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, knowledgeBaseId]);
    } else {
      onSelectionChange(selectedIds.filter((id) => id !== knowledgeBaseId));
    }
  };

  return (
    <div className="h-[200px] max-h-[200px] overflow-y-auto rounded-md border p-3">
      <InfiniteScrollList
        data={data}
        isLoading={isLoading}
        hasMore={hasMore}
        isInitialLoading={isInitialLoading}
        isFetchingNextPage={isFetchingNextPage}
        error={error}
        fetchNextPage={fetchNextPage}
        reload={reload}
        renderItem={(kb) => (
          <div key={kb.id} className="flex items-center space-x-2">
            <Checkbox
              id={`kb-${kb.id}`}
              checked={selectedIds.includes(kb.id)}
              onCheckedChange={(checked) => handleSelectionChange(kb.id, !!checked)}
            />
            <Label htmlFor={`kb-${kb.id}`} className="cursor-pointer text-sm">
              {kb.name}
            </Label>
          </div>
        )}
      />
    </div>
  );
}

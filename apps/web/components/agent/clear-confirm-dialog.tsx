"use client";

import { useMultiDeleteSessions } from "@/service/session-service";
import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { useTranslations } from "next-intl";

interface ClearConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  itemType: string; // 例如: "会话", "数据", "缓存"
  agentId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
  loading?: boolean;
  disabled?: boolean;
  customDescription?: string;
}

export const ClearConfirmDialog = ({
  open,
  onOpenChange,
  title,
  itemType,
  agentId,
  onSuccess,
  onCancel,
  loading = false,
  disabled = false,
  customDescription,
}: ClearConfirmDialogProps) => {
  const { toast } = useToast();
  const multiDeleteSessions = useMultiDeleteSessions();
  const t = useTranslations("agent.clear");

  const defaultTitle = title || t("title");
  const defaultDescription = customDescription || t("description", { itemType });

  const handleClear = async () => {
    try {
      await multiDeleteSessions.mutateAsync({
        agent_id: agentId,
        session_ids: [], // 空数组表示删除所有会话
      });

      toast({
        title: t("success"),
        description: t("success"),
      });

      onSuccess?.();
    } catch (error) {
      console.error("清空会话失败:", error);
      toast({
        title: t("failed"),
        description: t("failed"),
        variant: "destructive",
      });
    }
  };

  return (
    <ConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      title={defaultTitle}
      description={defaultDescription}
      onConfirm={handleClear}
      onCancel={onCancel}
      confirmText={t("confirmText")}
      cancelText={t("cancelText")}
      loading={loading || multiDeleteSessions.isPending}
      disabled={disabled}
    />
  );
};

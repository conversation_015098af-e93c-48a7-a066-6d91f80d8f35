"use client";

import { useDeleteAgent } from "@/service/agent-service";
import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { ReactNode } from "react";

interface DeleteConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  description?: string | ReactNode;
  itemScope?: string;
  agentId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
  loading?: boolean;
  disabled?: boolean;
}

export const DeleteConfirmDialog = ({
  open,
  onOpenChange,
  title,
  description,
  itemScope,
  agentId,
  onSuccess,
  onCancel,
  loading = false,
  disabled = false,
}: DeleteConfirmDialogProps) => {
  const { toast } = useToast();
  const router = useRouter();
  const deleteAgent = useDeleteAgent();
  const t = useTranslations("agent.delete");

  const defaultTitle = title || t("confirmTitle");
  const defaultDescription =
    description || (itemScope === "TEAM_PUBLIC" ? t("teamDescription") : t("personalDescription"));

  const handleDelete = () => {
    try {
      deleteAgent.mutate(
        { agent_id: agentId },
        {
          onSuccess: () => {
            toast({
              title: t("success"),
              description: t("success"),
            });
            onSuccess?.();
            router.push("/");
          },
          onError: () => {
            toast({
              title: t("failed"),
              description: t("failed"),
              variant: "destructive",
            });
          },
        }
      );
    } catch (error) {
      console.error("删除Agent失败:", error);
      toast({
        title: t("failed"),
        description: t("failed"),
        variant: "destructive",
      });
    }
  };

  return (
    <ConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      title={defaultTitle}
      description={defaultDescription}
      onConfirm={handleDelete}
      onCancel={onCancel}
      confirmText={t("confirmText")}
      cancelText={t("cancelText")}
      loading={loading || deleteAgent.isPending}
      disabled={disabled}
    />
  );
};

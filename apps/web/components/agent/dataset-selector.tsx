"use client";

import { Label } from "@ragtop-web/ui/components/label";
import { useMemo } from "react";

import { useDatasetsInfinite } from "@/service/dataset-service";
import { InfiniteScrollList } from "@ragtop-web/ui/components/infinite-scroll-list";
import { RadioGroup, RadioGroupItem } from "@ragtop-web/ui/components/radio-group";

interface DatasetSelectorProps {
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
  maxHeight?: string;
}

/**
 * 数据集选择器组件
 * 支持滚动加载和多选
 */
export function DatasetSelector({ selectedIds, onSelectionChange }: DatasetSelectorProps) {
  const {
    data,
    isLoading,
    hasMore,
    isInitialLoading,
    isFetchingNextPage,
    error,
    fetchNextPage,
    reload,
  } = useDatasetsInfinite(10);

  const selectedId = useMemo(() => {
    if (selectedIds) return selectedIds[0];
  }, [selectedIds]);

  // 处理选择变化
  const handleSelectionChange = (datasetId: string) => {
    onSelectionChange([datasetId]);
  };

  return (
    <div className="h-[200px] max-h-[200px] overflow-y-auto rounded-md border p-3">
      <RadioGroup value={selectedId} onValueChange={handleSelectionChange}>
        <InfiniteScrollList
          data={data}
          isLoading={isLoading}
          hasMore={hasMore}
          isInitialLoading={isInitialLoading}
          isFetchingNextPage={isFetchingNextPage}
          error={error}
          fetchNextPage={fetchNextPage}
          reload={reload}
          renderItem={(dataset) => (
            <div key={dataset.id} className="flex items-center space-x-2">
              <RadioGroupItem value={dataset.id} id={dataset.id} />
              <Label htmlFor={`dataset-${dataset.id}`} className="cursor-pointer text-sm">
                {dataset.name}
              </Label>
            </div>
          )}
        />
      </RadioGroup>
    </div>
  );
}

"use client";

import { Lay<PERSON>, Zap, Shield, Smartphone } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@ragtop-web/ui/components/card";

/**
 * 特性展示组件
 */
export function FeaturesSection() {
  const features = [
    {
      icon: <Layers className="text-primary h-10 w-10" />,
      title: "现代化架构",
      description: "基于 Next.js 和 React 构建的现代化应用程序架构，支持服务器组件和客户端组件。",
    },
    {
      icon: <Zap className="text-primary h-10 w-10" />,
      title: "高性能",
      description: "优化的性能和加载速度，提供流畅的用户体验。",
    },
    {
      icon: <Shield className="text-primary h-10 w-10" />,
      title: "安全可靠",
      description: "内置安全特性，保护您的数据和隐私。",
    },
    {
      icon: <Smartphone className="text-primary h-10 w-10" />,
      title: "响应式设计",
      description: "适应各种设备和屏幕尺寸的响应式设计。",
    },
  ];

  return (
    <section className="bg-muted/50 w-full py-12 md:py-24 lg:py-32">
      <div className="container px-4 md:px-6">
        <div className="mb-12 flex flex-col items-center gap-4 text-center">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">主要特性</h2>
          <p className="text-muted-foreground max-w-[700px] md:text-xl">
            探索我们应用程序的强大功能和特性
          </p>
        </div>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {features.map((feature, index) => (
            <Card key={index} className="bg-background">
              <CardHeader>
                <div className="mb-4">{feature.icon}</div>
                <CardTitle>{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}

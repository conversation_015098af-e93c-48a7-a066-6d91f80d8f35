import { Suspense } from "react";
import Client<PERSON>rovider from "./config-provider-client";

// 异步组件，等待配置初始化
// async function ConfigProvider({ children }: { children: React.ReactNode }) {
//   await connection();

//   try {
//     const cfg = {
//       API_PREFIX: process.env.NEXT_PUBLIC_API_PREFIX ?? "/api/v1/portal-ragtop",
//       API_BASE_URL: process.env.API_BASE_URL ?? "",
//       SHOW_NL2CODE: process.env.SHOW_NL2CODE ?? "true",
//       UPLOAD_MODE: process.env.UPLOAD_MODE ?? "",
//     };
//     return <ClientProvider config={cfg}>{children}</ClientProvider>;
//   } catch (error) {
//     throw error; // 让 Suspense 的错误边界处理
//   }
// }

export default function ConfigWrapper({
  cfg,
  children,
}: {
  cfg: Record<string, string>;
  children: React.ReactNode;
}) {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center">
          <div className="text-center">
            {/* <Loader2 className="w-8 h-8 animate-spin text-gray-900 mx-auto mb-4" /> */}
            {/* <p className="text-gray-600">正在加载配置...</p> */}
          </div>
        </div>
      }
    >
      <ClientProvider config={cfg}>{children}</ClientProvider>
    </Suspense>
  );
}

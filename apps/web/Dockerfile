ARG PACKAGE_NAME
FROM zbyte.tencentcloudcr.com/common/build/node:18-alpine AS base

FROM base AS builder
ARG PACKAGE_NAME
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.tencent.com/g' /etc/apk/repositories && \
    apk update && \
    apk add --no-cache libc6-compat && \
    npm config set registry http://10.3.0.9:8080/repository/npm-group/ && \
    npm install -g turbo && \
    npm cache clean --force

WORKDIR /app
COPY . .
RUN turbo prune ${PACKAGE_NAME} --docker && \
    rm -rf /app/out/full/*/*/node_modules


FROM base AS installer
WORKDIR /app
ARG PACKAGE_NAME
ARG DOCKER_BUILD
ENV DOCKER_BUILD=${DOCKER_BUILD}
ENV WEB_API_URL=${WEB_API_URL}
ENV WEB_INNER_API_URL=${WEB_INNER_API_URL}
ENV API_PREFIX=${API_PREFIX}
ENV SHOW_NL2CODE=${SHOW_NL2CODE}
ENV UPLOAD_MODE=${UPLOAD_MODE}
ENV FILE_PREVIEW_MAX_ROWS=${FILE_PREVIEW_MAX_ROWS}

ENV FILE_SIZE_LIMIT=${FILE_SIZE_LIMIT}
ENV TABLE_FILE_SIZE_LIMIT=${TABLE_FILE_SIZE_LIMIT}
COPY --from=builder /app/out/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=builder /app/out/pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --from=builder /app/out/json/ .
RUN npm config set registry http://10.3.0.9:8080/repository/npm-group/ && \
    npm install -g pnpm@10.4.1 && \
    pnpm install --frozen-lockfile

ENV NEXT_TELEMETRY_DISABLED 1
COPY --from=builder /app/out/full/ .
RUN NODE_OPTIONS="--max_old_space_size=8192" pnpm turbo run build --filter=${PACKAGE_NAME}...


FROM base AS runner
WORKDIR /app
ARG PACKAGE_NAME
ENV PACKAGE=${PACKAGE_NAME}
ARG DOCKER_BUILD
ENV NEXT_PUBLIC_DOCKER_BUILD=${DOCKER_BUILD}
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=installer /app/apps/${PACKAGE}/next.config.mjs .
COPY --from=installer /app/apps/${PACKAGE}/package.json .


COPY --from=installer --chown=nextjs:nodejs /app/apps/${PACKAGE}/.next/standalone ./
COPY --from=installer --chown=nextjs:nodejs /app/apps/${PACKAGE}/.next/server ./apps/${PACKAGE}/.next/server
COPY --from=installer --chown=nextjs:nodejs /app/apps/${PACKAGE}/.next/static ./apps/${PACKAGE}/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/apps/${PACKAGE}/public ./apps/${PACKAGE}/public

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD node apps/${PACKAGE}/server.js

import {
  FileArchive,
  FileAudio,
  FileCode,
  File as FileIcon,
  FileImage,
  FileSpreadsheet,
  FileText,
  FileVideo,
  FolderIcon,
} from "lucide-react";

// 获取文件图标
export const getFileIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case "folder":
      return <FolderIcon className="h-4 w-4 text-yellow-500" />;
    case "pdf":
      return <FileText className="h-4 w-4 text-red-500" />;
    case "word":
      return <FileText className="h-4 w-4 text-blue-500" />;
    case "xlsx":
      return <FileSpreadsheet className="h-4 w-4 text-green-500" />;
    case "pptx":
      return <FileText className="h-4 w-4 text-orange-500" />;
    case "text":
      return <FileText className="h-4 w-4 text-gray-500" />;
    case "csv":
      return <FileSpreadsheet className="h-4 w-4 text-green-400" />;
    case "image":
      return <FileImage className="h-4 w-4 text-purple-500" />;
    case "archive":
      return <FileArchive className="h-4 w-4 text-yellow-500" />;
    case "video":
      return <FileVideo className="h-4 w-4 text-pink-500" />;
    case "audio":
      return <FileAudio className="h-4 w-4 text-indigo-500" />;
    case "code":
      return <FileCode className="h-4 w-4 text-cyan-500" />;
    default:
      return <FileIcon className="h-4 w-4 text-gray-400" />;
  }
};

/**
 * Web应用专用的API客户端配置
 *
 * 基于UI库的fetch-client，为Web应用添加团队ID自动注入功能
 */

import { getTeamId } from "@/store/team-store";
import type { FetchOptions } from "@ragtop-web/ui/lib/api";
import {
  patch as basePatch,
  post as basePost,
  put as basePut,
  createApiClient,
} from "@ragtop-web/ui/lib/api";
import { connection } from "next/server";

/**
 * 需要排除team_id参数的路径
 */
const EXCLUDED_PATHS = ["/auth/signin", "/auth/signout", "/user/current"];

/**
 * 不需要认证的路径
 */
const NO_AUTH_PATHS = ["/auth/signin"];

/**
 * 检查路径是否需要添加team_id
 */
const shouldAddTeamId = (url: string): boolean => {
  return !EXCLUDED_PATHS.some((path) => url.includes(path));
};

/**
 * 检查路径是否需要认证
 */
const shouldRequireAuth = (url: string): boolean => {
  return !NO_AUTH_PATHS.some((path) => url.includes(path));
};

/**
 * Web应用专用的POST请求
 * 自动为非排除路径添加team_id参数，自动设置requireAuth
 */
export const post = <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
  const { isPaginated, pageNumber, pageSize, requireTeamId = true, ...restOptions } = options;

  // 准备请求数据
  let finalData = data || {};

  // 添加分页参数
  if (isPaginated) {
    finalData = {
      ...finalData,
      page_number: pageNumber || 1,
      page_size: pageSize || 10,
    };
  }

  // 添加团队ID到请求体（只有在 requireTeamId 为 true 时）
  if (requireTeamId && shouldAddTeamId(url)) {
    const teamId = getTeamId();
    if (teamId) {
      finalData = {
        ...finalData,
        team_id: teamId,
      };
    }
  }

  // 自动设置requireAuth
  const finalOptions = {
    ...restOptions,
    requireAuth: shouldRequireAuth(url),
    requireTeamId: requireTeamId && shouldAddTeamId(url),
  };
  return basePost<T>(url, finalData, finalOptions);
};

/**
 * Web应用专用的PUT请求
 * 自动为非排除路径添加team_id参数，自动设置requireAuth
 */
export const put = <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
  const { isPaginated, pageNumber, pageSize, ...restOptions } = options;

  // 准备请求数据
  let finalData = data || {};

  // 添加分页参数
  if (isPaginated) {
    finalData = {
      ...finalData,
      page_number: pageNumber || 1,
      page_size: pageSize || 10,
    };
  }

  // 添加团队ID到请求体
  if (shouldAddTeamId(url)) {
    const teamId = getTeamId();
    if (teamId) {
      finalData = {
        ...finalData,
        team_id: teamId,
      };
    }
  }

  // 自动设置requireAuth
  const finalOptions = {
    ...restOptions,
    requireAuth: shouldRequireAuth(url),
  };

  return basePut<T>(url, finalData, finalOptions);
};

/**
 * Web应用专用的PATCH请求
 * 自动为非排除路径添加team_id参数，自动设置requireAuth
 */
export const patch = <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
  const { isPaginated, pageNumber, pageSize, ...restOptions } = options;

  // 准备请求数据
  let finalData = data || {};

  // 添加分页参数
  if (isPaginated) {
    finalData = {
      ...finalData,
      page_number: pageNumber || 1,
      page_size: pageSize || 10,
    };
  }

  // 添加团队ID到请求体
  if (shouldAddTeamId(url)) {
    const teamId = getTeamId();
    if (teamId) {
      finalData = {
        ...finalData,
        team_id: teamId,
      };
    }
  }

  // 自动设置requireAuth
  const finalOptions = {
    ...restOptions,
    requireAuth: shouldRequireAuth(url),
  };

  return basePatch<T>(url, finalData, finalOptions);
};

/**
 * 创建Web应用专用的API客户端
 * 自动为非排除路径添加team_id参数，自动设置requireAuth
 */
export const createWebApiClient = async (baseConfig: Partial<FetchOptions> = {}) => {
  await connection();

  const baseClient = createApiClient({
    ...baseConfig,
    baseUrl: process.env.WEB_API_URL || "",
    apiPrefix: process.env.API_PREFIX || "",
  });

  // 公共请求处理逻辑
  const processRequest = (url: string, data: any, options: FetchOptions) => {
    const {
      isPaginated,
      pageNumber,
      pageSize,
      requireTeamId = baseConfig?.requireTeamId !== false,
      ...restOptions
    } = options;

    // 准备请求数据
    let finalData = data || {};

    // 添加分页参数
    if (isPaginated) {
      finalData = {
        ...finalData,
        page_number: pageNumber || 1,
        page_size: pageSize || 10,
      };
    }

    // 添加团队ID到请求体（只有在 requireTeamId 为 true 时）
    if (requireTeamId && shouldAddTeamId(url)) {
      const teamId = getTeamId();
      if (teamId) {
        finalData = {
          ...finalData,
          team_id: teamId,
        };
      }
    }

    // 自动设置requireAuth
    const finalOptions = {
      ...baseConfig,
      ...restOptions,
      requireAuth: shouldRequireAuth(url),
      requireTeamId: requireTeamId && shouldAddTeamId(url),
    };

    return { finalData, finalOptions };
  };

  return {
    ...baseClient,
    post: <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
      const { finalData, finalOptions } = processRequest(url, data, options);
      return baseClient.post<T>(url, finalData, finalOptions);
    },
    put: <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
      const { finalData, finalOptions } = processRequest(url, data, options);
      return baseClient.put<T>(url, finalData, finalOptions);
    },
    patch: <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
      const { finalData, finalOptions } = processRequest(url, data, options);
      return baseClient.patch<T>(url, finalData, finalOptions);
    },
  };
};

// 重新导出其他需要的函数和类型
export {
  API_BAD_CREDENTIAL_CODE,
  API_SUCCESS_CODE,
  ApiError,
  clearAccessToken,
  clearTeamId,
  del,
  fetchClient,
  get,
  getAccessToken,
  setAccessToken,
  setTeamId,
  type ApiResponse,
  type FetchOptions,
  type PaginatedResponse,
} from "@ragtop-web/ui/lib/api";

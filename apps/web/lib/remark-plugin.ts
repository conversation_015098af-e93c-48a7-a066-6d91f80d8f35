// /* eslint-disable no-param-reassign */
// // remarkCustomPlugin.ts
// // https://www.npmjs.com/package/remark-directive/v/2.0.1
import { h } from "hastscript";
import { visit } from "unist-util-visit";

interface DirectiveNode extends Node {
  name: string;
  attributes: Record<string, any>;
  children?: any[];
  data?: {
    hName?: string;
    hProperties?: Record<string, any>;
    hChildren?: any[];
    index?: number;
  };
}

function remarkPlugin() {
  return (tree: any) => {
    // textDirective 表示:quote[11]
    // leafDirective 表示::quote[11] (非行内式，连续:quote[11] ::quote[11]会无法识别)
    // containerDirective 表示:::quote[11]{attribute: 可扩展}
    // 先处理所有 quote 指令节点

    visit(tree, (node: any) => {
      if (
        node.type === "textDirective" ||
        node.type === "leafDirective" ||
        node.type === "containerDirective"
      ) {
        const name = node.name;
        // 先获取 label 内容：textDirective 用 children，leaf/container 用 label
        const label = node.children?.[0]?.value ?? node.children?.[0]?.children?.[0]?.value ?? "";
        if (name !== "quote") {
          node.type = "text";
          node.value = `:${name}${label}`;
          delete node.children;
          delete node.attributes;
        } else {
          const directiveNode = node as DirectiveNode;
          const data = directiveNode.data || (directiveNode.data = {});
          const hast = h(name, directiveNode.attributes || {}, label);
          data.hName = hast.tagName;
          data.hProperties = hast.properties;
          data.hChildren = hast.children;
        }
      }
    });
  };
}

export default remarkPlugin;

/**
 * 用户角色工具函数
 *
 * 提供检查用户角色和权限的工具函数
 */

import {
  currentTeamAtom,
  currentUserAtom,
  getCurrentTeam,
  isTeamAdminAtom,
} from "@/store/team-store";
import { useAtomValue } from "jotai";

/**
 * React Hook版本的getCurrentTeam
 *
 * 使用Jotai状态管理，从全局状态中获取当前团队信息
 *
 * @returns 当前团队信息，如果未找到则返回null
 */
export const useCurrentTeam = () => {
  return useAtomValue(currentTeamAtom);
};

/**
 * React Hook版本的获取当前用户信息
 *
 * 使用Jotai状态管理，从全局状态中获取当前用户信息
 *
 * @returns 用户信息对象，如果未找到则返回null
 */
export const useCurrentUser = () => {
  return useAtomValue(currentUserAtom);
};

/**
 * React Hook版本的isTeamAdmin
 *
 * 使用Jotai状态管理，从全局状态中获取管理员状态
 *
 * @returns 如果用户是管理员则返回true，否则返回false
 */
export const useIsTeamAdmin = (): boolean => {
  return useAtomValue(isTeamAdminAtom);
};

/**
 * React Hook版本的获取当前用户在当前团队中的角色
 *
 * @returns 用户角色数组，如果未找到则返回空数组
 */
export const useCurrentTeamRoles = (): string[] => {
  const currentTeam = useAtomValue(currentTeamAtom);

  try {
    // 返回用户在当前团队中的角色
    return currentTeam?.roles || [];
  } catch (error) {
    console.error("获取用户角色时出错:", error);
    return [];
  }
};

/**
 * 判断当前用户是否是owner
 */
export const checkIsOwner = (owner_id: string, isAdmin: boolean): boolean => {
  // 如果owner_id为空，则认为都可以处理
  // 如果用户不是管理员，说明他没有操作权限，则认为其他人都可以处理
  if (!owner_id || !isAdmin) {
    return true;
  }
  const currentTeam = getCurrentTeam();
  return currentTeam?.member_id === owner_id;
};

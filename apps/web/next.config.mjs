import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin("./i18n.ts");

/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    WEB_INNER_API_URL: process.env.WEB_INNER_API_URL,
    WEB_API_URL: process.env.WEB_API_URL,
    API_PREFIX: process.env.API_PREFIX,
    SHOW_NL2CODE: process.env.SHOW_NL2CODE,
    UPLOAD_MODE: process.env.UPLOAD_MODE,
    FILE_SIZE_LIMIT: process.env.FILE_SIZE_LIMIT,
    TABLE_FILE_SIZE_LIMIT: process.env.TABLE_FILE_SIZE_LIMIT,
  },
  transpilePackages: ["@ragtop-web/ui"],
  output: "standalone",
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
};

export default withNextIntl(nextConfig);

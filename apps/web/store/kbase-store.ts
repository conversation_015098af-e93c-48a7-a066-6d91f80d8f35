/**
 * 团队状态管理
 *
 * 使用Jotai管理团队相关的状态，结合localStorage持久化存储
 */

import { atom } from "jotai";

type ParserConfig = {
  parse_model: string;
  file_extensions: string[];
};

type FileExtensionMap = {
  [extension: string]: string[];
};

function groupByFileExtension(data: ParserConfig[] | null): FileExtensionMap {
  const result: FileExtensionMap = {};
  if (!data) {
    return {};
  }
  data.forEach(({ parse_model, file_extensions }) => {
    file_extensions.forEach((ext) => {
      if (!result[ext]) {
        result[ext] = [];
      }
      result[ext].push(parse_model);
    });
  });

  return result;
}

const getAllExtensions = (input: ParserConfig[] | null) => {
  if (input) {
    return Array.from(new Set(input.flatMap((item) => item.file_extensions)));
  }
  return [];
};

/**
 * 当前支持的parse方法及对应的文件类型
 */
export const parseMethodAtom = atom<ParserConfig[] | null>(null);

/**
 * 不同文件类型支持的parse方法
 */
export const fileExtensionAtom = atom((get) => {
  const methods = get(parseMethodAtom);
  const fileExtension = groupByFileExtension(methods);
  return fileExtension || false;
});

export const allExtensionAtom = atom((get) => {
  const methods = get(parseMethodAtom);
  const allExtension = getAllExtensions(methods);
  return allExtension;
});

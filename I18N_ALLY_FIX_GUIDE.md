# i18n-ally "Key not found" 问题修复指南

本文档说明如何修复 i18n-ally 插件中出现的 "Key not found" 错误，即使翻译键实际存在的情况。

## 🔍 问题分析

i18n-ally 插件报告 "Key not found" 错误的常见原因：

1. **路径配置错误**: 插件无法找到正确的翻译文件路径
2. **命名空间配置问题**: next-intl 使用嵌套命名空间，插件可能无法正确解析
3. **正则表达式匹配问题**: 插件无法正确识别 `useTranslations()` 的使用模式
4. **缓存问题**: 插件缓存了错误的配置信息

## ✅ 已实施的修复

### 1. 更新 VSCode 配置 (`.vscode/settings.json`)

```json
{
  // 修正路径配置，只指向 web 应用
  "i18n-ally.localesPaths": ["apps/web/messages"],
  
  // 启用命名空间支持
  "i18n-ally.namespace": true,
  
  // 添加 next-intl 特定的正则表达式
  "i18n-ally.regex.usageMatchAppend": [
    "(?:useTranslations|getTranslations)\\s*\\(\\s*['\"`]([^'\"`.]+(?:\\.[^'\"`.]+)*)['\"`]\\s*\\).*?\\bt\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)",
    "\\bt\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)"
  ],
  
  // 启用自动检测和重新加载
  "i18n-ally.detection.autoDetection": true,
  "i18n-ally.fullReloadOnChanged": true,
  
  // 启用 TypeScript 解析器
  "i18n-ally.parsers.typescript": {
    "enabled": true
  }
}
```

### 2. 修正 inlang 配置 (`project.inlang/settings.json`)

```json
{
  "plugin.inlang.nextIntl": {
    "pathPattern": "./apps/web/messages/{languageTag}.json"
  }
}
```

### 3. 创建自定义框架配置

创建了 `.vscode/i18n-ally-custom-framework.yml` 来支持 next-intl 的特殊用法模式。

### 4. 添加测试和重置脚本

- `pnpm i18n:test` - 测试翻译键是否存在
- `pnpm i18n:reset` - 重置 i18n-ally 缓存

## 🛠️ 手动修复步骤

如果问题仍然存在，请按以下步骤操作：

### 步骤 1: 重启 VSCode
```bash
# 完全关闭 VSCode，然后重新打开
```

### 步骤 2: 重新加载 i18n-ally
1. 打开命令面板 (`Ctrl+Shift+P` / `Cmd+Shift+P`)
2. 运行 `i18n Ally: Reload`
3. 运行 `Developer: Reload Window`

### 步骤 3: 检查插件状态
- 查看 VSCode 状态栏是否显示正确的语言数量 (应该显示 "zh, en")
- 检查是否显示翻译键的总数

### 步骤 4: 验证配置
运行测试脚本验证翻译键是否正确加载：
```bash
pnpm i18n:test
```

### 步骤 5: 强制重置 (如果需要)
```bash
pnpm i18n:reset
```

## 🔧 配置验证清单

- [ ] `.vscode/settings.json` 中的 `i18n-ally.localesPaths` 指向正确路径
- [ ] `i18n-ally.namespace` 设置为 `true`
- [ ] `project.inlang/settings.json` 中的路径配置正确
- [ ] 翻译文件 (`apps/web/messages/zh.json`, `apps/web/messages/en.json`) 存在且格式正确
- [ ] VSCode 中安装了 i18n Ally 插件
- [ ] 插件状态栏显示正确的语言信息

## 🐛 常见问题排查

### 问题: 插件仍然报告 "Key not found"
**解决方案:**
1. 检查翻译键的完整路径是否正确
2. 确认使用的是 `useTranslations("namespace")` 而不是 `useTranslations("namespace.subkey")`
3. 重启 VSCode

### 问题: 插件无法识别 `useTranslations` 调用
**解决方案:**
1. 确认正则表达式配置正确
2. 检查 TypeScript 解析器是否启用
3. 验证文件扩展名是否在支持列表中

### 问题: 命名空间不工作
**解决方案:**
1. 确认 `i18n-ally.namespace` 设置为 `true`
2. 检查翻译文件的嵌套结构是否正确
3. 验证 `i18n-ally.keystyle` 设置为 `"nested"`

## 📊 验证结果

运行 `pnpm i18n:test` 应该显示：
```
🎉 所有测试通过！
```

如果看到这个消息，说明翻译键配置正确，i18n-ally 应该能够正常工作。

## 🚀 最佳实践

1. **保持配置一致**: 确保所有配置文件指向相同的翻译文件路径
2. **定期测试**: 使用 `pnpm i18n:test` 定期验证翻译键
3. **重启插件**: 修改配置后总是重启 VSCode 或重新加载插件
4. **检查状态栏**: 使用 i18n-ally 状态栏信息来验证插件状态

## 📝 相关文件

- `.vscode/settings.json` - VSCode 和 i18n-ally 配置
- `project.inlang/settings.json` - inlang 项目配置
- `apps/web/messages/zh.json` - 中文翻译文件
- `apps/web/messages/en.json` - 英文翻译文件
- `scripts/test-i18n.js` - 翻译键测试脚本
- `scripts/reset-i18n-ally.js` - 插件重置脚本

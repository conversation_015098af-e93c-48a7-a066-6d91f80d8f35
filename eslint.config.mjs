import eslintConfig from "@ragtop-web/eslint-config/index.js";

export default [
  ...eslintConfig,
  {
    ignores: [
      "node_modules/**",
      "dist/**",
      ".next/**",
      "out/**",
      "build/**",
      "coverage/**",
      "*.config.js",
      "*.config.ts",
      "*.config.mjs",
      "**/.next/**",
      "**/dist/**",
      "**/build/**",
      "**/coverage/**",
      "**/node_modules/**",
      "**/*.d.ts",
      "apps/*/public/**",
      "packages/*/dist/**",
    ],
  },
];

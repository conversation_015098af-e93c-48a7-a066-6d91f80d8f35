{"name": "@ragtop-web/ui", "version": "0.0.0", "type": "module", "imports": {"#*": "./src/*"}, "scripts": {"lint": "eslint . --max-warnings 0"}, "dependencies": {"@hookform/resolvers": "5.0.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-avatar": "1.1.9", "@radix-ui/react-checkbox": "1.3.1", "@radix-ui/react-collapsible": "1.1.10", "@radix-ui/react-dialog": "1.1.13", "@radix-ui/react-dropdown-menu": "2.1.14", "@radix-ui/react-label": "2.1.6", "@radix-ui/react-popover": "1.1.13", "@radix-ui/react-progress": "1.1.3", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-select": "2.2.4", "@radix-ui/react-separator": "1.1.6", "@radix-ui/react-slider": "1.3.4", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "1.2.4", "@radix-ui/react-tabs": "1.1.11", "@radix-ui/react-toggle": "1.1.8", "@radix-ui/react-tooltip": "1.2.6", "@tanstack/react-query": "^5.76.1", "@tanstack/react-table": "8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "lucide-react": "^0.525.0", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "7.56.3", "sonner": "2.0.3", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4", "vega": "5", "vega-embed": "^6.29.0", "vega-lite": "5", "zod": "^3.24.2"}, "devDependencies": {"@ragtop-web/eslint-config": "workspace:*", "@ragtop-web/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.0.8", "@turbo/gen": "^2.4.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4.0.8", "typescript": "^5.7.3"}, "exports": {"./globals.css": "./src/styles/globals.css", "./styles/globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./lib/api": "./src/lib/api/fetch-client.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": ["./src/hooks/*.ts", "./src/hooks/*.tsx"]}}
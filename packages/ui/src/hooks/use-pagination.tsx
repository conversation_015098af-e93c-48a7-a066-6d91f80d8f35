"use client";

import { useState, useCallback } from "react";

/**
 * 分页状态管理接口
 */
export interface PaginationState {
  /** 当前页码 */
  pageNumber: number;
  /** 每页大小 */
  pageSize: number;
}

/**
 * 分页状态管理Hook的配置
 */
export interface UsePaginationConfig {
  /** 初始页码，默认为1 */
  initialPage?: number;
  /** 初始每页大小，默认为10 */
  initialPageSize?: number;
  /** 页码变化时的回调 */
  onPageChange?: (page: number) => void;
  /** 每页大小变化时的回调 */
  onPageSizeChange?: (pageSize: number) => void;
}

/**
 * 分页状态管理Hook的返回值
 */
export interface UsePaginationReturn {
  /** 当前页码 */
  pageNumber: number;
  /** 每页大小 */
  pageSize: number;
  /** 设置页码 */
  setPageNumber: (page: number) => void;
  /** 设置每页大小 */
  setPageSize: (size: number) => void;
  /** 重置到第一页 */
  resetToFirstPage: () => void;
  /** 处理页码变化 */
  handlePageChange: (page: number) => void;
  /** 处理每页大小变化 */
  handlePageSizeChange: (size: number) => void;
}

/**
 * 通用分页状态管理Hook
 *
 * @param config 配置参数
 * @returns 分页状态和操作方法
 */
export function usePagination({
  initialPage = 1,
  initialPageSize = 10,
  onPageChange,
  onPageSizeChange,
}: UsePaginationConfig = {}): UsePaginationReturn {
  const [pageNumber, setPageNumber] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);

  /**
   * 处理页码变化
   */
  const handlePageChange = useCallback(
    (page: number) => {
      setPageNumber(page);
      onPageChange?.(page);
    },
    [onPageChange]
  );

  /**
   * 处理每页大小变化
   */
  const handlePageSizeChange = useCallback(
    (size: number) => {
      setPageSize(size);
      // 当每页大小变化时，重置到第一页
      setPageNumber(1);
      onPageSizeChange?.(size);
      onPageChange?.(1);
    },
    [onPageChange, onPageSizeChange]
  );

  /**
   * 重置到第一页
   */
  const resetToFirstPage = useCallback(() => {
    setPageNumber(1);
    onPageChange?.(1);
  }, [onPageChange]);

  return {
    pageNumber,
    pageSize,
    setPageNumber,
    setPageSize,
    resetToFirstPage,
    handlePageChange,
    handlePageSizeChange,
  };
}

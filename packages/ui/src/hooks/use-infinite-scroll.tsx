"use client";

import { useInfiniteQuery } from "@tanstack/react-query";
import { useCallback, useEffect, useRef, useState } from "react";

/**
 * 无限滚动参数接口
 */
export interface InfiniteScrollParams {
  pageNumber?: number;
  pageSize?: number;
  [key: string]: any;
}

/**
 * 无限滚动响应接口
 */
export interface InfiniteScrollResponse<T> {
  records: T[];
  total: number;
  [key: string]: any;
}

/**
 * 基于 React Query 的无限滚动配置接口
 */
export interface UseInfiniteQueryScrollConfig<T> {
  /** 查询键 */
  queryKey: (string | number | boolean | object)[];
  /** 获取数据函数 */
  queryFn: (
    params: InfiniteScrollParams & { pageParam?: number }
  ) => Promise<InfiniteScrollResponse<T>>;
  /** 每页大小，默认 20 */
  pageSize?: number;
  /** 是否启用，默认 true */
  enabled?: boolean;
  /** 额外的请求参数 */
  extraParams?: Record<string, any>;
  /** 滚动容器选择器，默认为 window */
  containerSelector?: string;
  /** 距离底部多少像素时触发加载，默认 200px */
  threshold?: number;
  /** 数据缓存时间，默认 5 分钟 */
  staleTime?: number;
}

/**
 * 基于 React Query 的无限滚动 Hook 返回值接口
 */
export interface UseInfiniteQueryScrollReturn<T> {
  /** 数据列表 */
  data: T[];
  /** 是否加载中 */
  isLoading: boolean;
  /** 是否还有更多数据 */
  hasMore: boolean;
  /** 是否初始加载中 */
  isInitialLoading: boolean;
  /** 是否正在获取下一页 */
  isFetchingNextPage: boolean;
  /** 错误信息 */
  error: string | null;
  /** 重新加载 */
  reload: () => void;
  /** 加载下一页 */
  fetchNextPage: () => void;
  /** 总数据量 */
  total: number;
  /** 当前页码 */
  currentPage: number;
}

/**
 * 传统无限滚动配置接口（向后兼容）
 */
export interface UseInfiniteScrollConfig<T> {
  /** 获取数据函数 */
  fetchFn: (params: InfiniteScrollParams) => Promise<InfiniteScrollResponse<T>>;
  /** 每页大小，默认 20 */
  pageSize?: number;
  /** 是否启用，默认 true */
  enabled?: boolean;
  /** 额外的请求参数 */
  extraParams?: Record<string, any>;
  /** 滚动容器选择器，默认为 window */
  containerSelector?: string;
  /** 距离底部多少像素时触发加载，默认 200px */
  threshold?: number;
}

/**
 * 传统无限滚动 Hook 返回值接口（向后兼容）
 */
export interface UseInfiniteScrollReturn<T> {
  /** 数据列表 */
  data: T[];
  /** 是否加载中 */
  isLoading: boolean;
  /** 是否还有更多数据 */
  hasMore: boolean;
  /** 是否初始加载中 */
  isInitialLoading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 重新加载 */
  reload: () => void;
  /** 总数据量 */
  total: number;
  /** 当前页码 */
  currentPage: number;
}

/**
 * 基于 React Query 的无限滚动 Hook
 *
 * @param config 配置参数
 * @returns 无限滚动状态和方法
 */
export function useInfiniteQueryScroll<T>({
  queryKey,
  queryFn,
  pageSize = 20,
  enabled = true,
  extraParams = {},
  containerSelector,
  threshold = 200,
  staleTime = 5 * 60 * 1000, // 5分钟
  ...rest
}: UseInfiniteQueryScrollConfig<T>): UseInfiniteQueryScrollReturn<T> {
  // 使用 React Query 的 useInfiniteQuery
  const {
    data: queryData,
    error: queryError,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
    isLoading: isInitialLoading,
    refetch,
  } = useInfiniteQuery({
    queryKey: [...queryKey, pageSize, extraParams],
    queryFn: ({ pageParam = 1 }) =>
      queryFn({
        pageNumber: pageParam,
        pageSize,
        ...extraParams,
        pageParam,
      }),
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage || !lastPage.records) return undefined;

      const currentCount = allPages.reduce((total, page) => total + (page.records?.length || 0), 0);
      const hasMore = currentCount < (lastPage.total || 0);

      return hasMore ? allPages.length + 1 : undefined;
    },
    initialPageParam: 1,
    enabled,
    staleTime,
    ...rest,
  });

  // 合并所有页面的数据
  const data = queryData?.pages.flatMap((page) => page.records || []) || [];

  // 获取总数（从最后一页获取）
  const total = queryData?.pages[queryData.pages.length - 1]?.total || 0;

  // 当前页码
  const currentPage = queryData?.pages.length || 0;

  // 是否还有更多数据
  const hasMore = hasNextPage || false;

  // 错误信息
  const error = queryError ? (queryError as Error).message : null;

  /**
   * 滚动事件处理
   */
  const handleScroll = useCallback(() => {
    if (!enabled || isFetching || !hasMore || error) return;

    const container = containerSelector ? document.querySelector(containerSelector) : window;

    if (!container) return;

    let scrollTop: number;
    let scrollHeight: number;
    let clientHeight: number;

    if (container === window) {
      scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      scrollHeight = document.documentElement.scrollHeight;
      clientHeight = window.innerHeight;
    } else {
      const element = container as Element;
      scrollTop = element.scrollTop;
      scrollHeight = element.scrollHeight;
      clientHeight = element.clientHeight;
    }

    // 当滚动到距离底部 threshold 像素时加载更多
    if (scrollHeight - scrollTop <= clientHeight + threshold) {
      fetchNextPage();
    }
  }, [enabled, isFetching, hasMore, error, fetchNextPage, containerSelector, threshold]);

  // 绑定滚动事件
  useEffect(() => {
    if (!enabled) return;

    const container = containerSelector ? document.querySelector(containerSelector) : window;

    if (!container) return;

    container.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [handleScroll, enabled, containerSelector]);

  /**
   * 重新加载
   */
  const reload = useCallback(() => {
    refetch();
  }, [refetch]);

  return {
    data,
    isLoading: isFetching,
    hasMore,
    isInitialLoading,
    isFetchingNextPage,
    error,
    reload,
    fetchNextPage,
    total,
    currentPage,
  };
}

/**
 * 传统无限滚动 Hook（向后兼容）
 *
 * @param config 配置参数
 * @returns 无限滚动状态和方法
 */
export function useInfiniteScroll<T>({
  fetchFn,
  pageSize = 20,
  enabled = true,
  extraParams = {},
  containerSelector,
  threshold = 200,
}: UseInfiniteScrollConfig<T>): UseInfiniteScrollReturn<T> {
  const [data, setData] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);

  // 请求状态引用 - 用于防止重复请求
  const requestRef = useRef<{
    isRequesting: boolean;
    lastPage: number;
  }>({
    isRequesting: false,
    lastPage: 0,
  });

  /**
   * 获取数据
   */
  const fetchData = useCallback(
    async (page: number, reset: boolean = false) => {
      if (!enabled) return;

      // 如果已经在请求中，直接返回
      if (requestRef.current.isRequesting) {
        return;
      }

      // 如果是相同页面且不是重置，直接返回
      if (!reset && page === requestRef.current.lastPage) {
        return;
      }

      // 标记请求状态
      requestRef.current.isRequesting = true;

      // 设置加载状态
      if (reset || page === 1) {
        setIsInitialLoading(true);
      } else {
        setIsLoading(true);
      }

      setError(null);

      try {
        const params: InfiniteScrollParams = {
          pageNumber: page,
          pageSize,
          ...extraParams,
        };

        const response = await fetchFn(params);

        // 更新请求参数记录
        requestRef.current.lastPage = page;

        if (response && response.records && Array.isArray(response.records)) {
          // 更新数据列表
          if (reset || page === 1) {
            setData(response.records);
            setCurrentPage(1);
          } else {
            setData((prev) => [...prev, ...response.records]);
            setCurrentPage(page);
          }

          // 更新总数
          setTotal(response.total || 0);

          // 判断是否还有更多数据
          const currentCount = page * pageSize;
          setHasMore(currentCount < (response.total || 0));
        } else {
          // 处理空响应
          if (reset || page === 1) {
            setData([]);
            setCurrentPage(1);
          }
          setHasMore(false);
          setTotal(0);
        }
      } catch (err) {
        console.error("获取数据失败:", err);
        setError("获取数据失败");
        setData([]);
        setTotal(0);
        setCurrentPage(1);
        setHasMore(false); // 确保不会继续请求
      } finally {
        setIsLoading(false);
        setIsInitialLoading(false);
        requestRef.current.isRequesting = false;
      }
    },
    [fetchFn, pageSize, enabled, extraParams]
  );

  /**
   * 加载更多数据
   */
  const loadMore = useCallback(() => {
    if (!isLoading && !isInitialLoading && hasMore && !error && !requestRef.current.isRequesting) {
      const nextPage = currentPage + 1;
      fetchData(nextPage, false);
    }
  }, [fetchData, isLoading, isInitialLoading, hasMore, error, currentPage]);

  /**
   * 重新加载
   */
  const reload = useCallback(() => {
    setCurrentPage(1);
    setHasMore(true);
    setError(null); // 清除错误状态
    requestRef.current = {
      isRequesting: false,
      lastPage: 0,
    };
    fetchData(1, true);
  }, [fetchData]);

  /**
   * 滚动事件处理
   */
  const handleScroll = useCallback(() => {
    if (!enabled || isLoading || isInitialLoading || !hasMore || error) return;

    const container = containerSelector ? document.querySelector(containerSelector) : window;

    if (!container) return;

    let scrollTop: number;
    let scrollHeight: number;
    let clientHeight: number;

    if (container === window) {
      scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      scrollHeight = document.documentElement.scrollHeight;
      clientHeight = window.innerHeight;
    } else {
      const element = container as Element;
      scrollTop = element.scrollTop;
      scrollHeight = element.scrollHeight;
      clientHeight = element.clientHeight;
    }

    // 当滚动到距离底部 threshold 像素时加载更多
    if (scrollHeight - scrollTop <= clientHeight + threshold) {
      loadMore();
    }
  }, [
    enabled,
    isLoading,
    isInitialLoading,
    hasMore,
    error,
    loadMore,
    containerSelector,
    threshold,
  ]);

  // 绑定滚动事件
  useEffect(() => {
    if (!enabled) return;

    const container = containerSelector ? document.querySelector(containerSelector) : window;

    if (!container) return;

    container.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [handleScroll, enabled, containerSelector]);

  // 初始化数据加载
  useEffect(() => {
    if (enabled && data.length === 0 && !requestRef.current.isRequesting && !error) {
      fetchData(1, true);
    }
  }, [enabled, fetchData, data.length]);

  return {
    data,
    isLoading,
    hasMore,
    isInitialLoading,
    error,
    reload,
    total,
    currentPage,
  };
}

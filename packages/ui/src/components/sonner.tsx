"use client";

import { useTheme } from "next-themes";
import { Toaster as Son<PERSON>, ToasterProps } from "sonner";

interface CustomToasterProps extends ToasterProps {
  duration?: number; // 接受自定义持续时间
}

const Toaster = ({ duration = 2000, ...props }: CustomToasterProps) => {
  const { resolvedTheme = "system" } = useTheme();

  return (
    <Sonner
      theme={resolvedTheme as ToasterProps["theme"]}
      duration={duration} // 统一设置持续时间
      className="toaster group"
      style={
        {
          "--normal-bg": "var(--popover)",
          "--normal-text": "var(--popover-foreground)",
          "--normal-border": "var(--border)",
        } as React.CSSProperties
      }
      {...props}
    />
  );
};

export { Toaster };

"use client";

import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
} from "@ragtop-web/ui/components/sheet";
import { cn } from "@ragtop-web/ui/lib/utils";

interface CustomDrawerProps {
  open: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
}

export function CustomDrawer({
  open,
  onClose,
  title,
  children,
  footer,
  className,
}: CustomDrawerProps) {
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent className={cn("w-full overflow-y-auto px-4 sm:max-w-md", className)}>
        <SheetHeader className="p-0 py-4">
          <div className="flex items-center justify-between">
            <SheetTitle>{title}</SheetTitle>
          </div>
        </SheetHeader>
        {children}
        {footer ? <SheetFooter>{footer}</SheetFooter> : null}
      </SheetContent>
    </Sheet>
  );
}

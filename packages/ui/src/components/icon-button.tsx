"use client";

import { type VariantProps } from "class-variance-authority";
import * as React from "react";

import { Button, buttonVariants } from "./button.tsx";
import { Tooltip, TooltipContent, TooltipTrigger } from "./tooltip.tsx";

interface IconButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  tooltip?: React.ReactNode;
  tooltipSide?: "top" | "right" | "bottom" | "left";
  tooltipAlign?: "start" | "center" | "end";
  asChild?: boolean;
}

function IconButton({
  children,
  tooltip,
  tooltipSide = "top",
  tooltipAlign = "center",
  disabled = false,
  onClick,
  className,
  variant,
  size = "icon",
  asChild = false,
  ...props
}: IconButtonProps) {
  const button = (
    <Button
      variant={variant}
      size={size}
      disabled={disabled}
      onClick={onClick}
      className={className}
      asChild={asChild}
      {...props}
    >
      {children}
    </Button>
  );

  // 如果没有tooltip，直接返回按钮
  if (!tooltip) {
    return button;
  }

  // 现在disabled的button也可以接收hover事件了，所以不需要特殊处理
  return (
    <Tooltip>
      <TooltipTrigger asChild>{button}</TooltipTrigger>
      <TooltipContent side={tooltipSide} align={tooltipAlign}>
        {tooltip}
      </TooltipContent>
    </Tooltip>
  );
}

export { IconButton };
export type { IconButtonProps };

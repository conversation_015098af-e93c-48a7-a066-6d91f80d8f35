import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@ragtop-web/ui/components/breadcrumb";
import { Button } from "@ragtop-web/ui/components/button";
import { cn } from "@ragtop-web/ui/lib/utils";
import { ChevronLeft } from "lucide-react";

export interface BreadcrumbItem {
  title: string;
  href: string;
  isCurrent?: boolean;
  linkComponent?: React.ComponentType<any>;
}

export interface CustomContainerProps {
  title: string;
  action?: React.ReactNode;
  children: React.ReactNode;
  breadcrumbs?: BreadcrumbItem[];
  backHref?: string;
  className?: string;
}

export function CustomContainer({
  title,
  action,
  children,
  breadcrumbs,
  backHref,
  className,
}: CustomContainerProps) {
  const showBreadcrumbs = breadcrumbs && breadcrumbs.length > 0;
  const showBackButton = !!backHref;

  return (
    <div className={cn("container", className)}>
      {/* 面包屑导航 */}
      {showBreadcrumbs && (
        <Breadcrumb className="mb-4">
          <BreadcrumbList>
            {breadcrumbs.map((item, index) => (
              <div key={item.href} className="flex items-center">
                {index > 0 && <BreadcrumbSeparator />}
                <BreadcrumbItem>
                  {item.isCurrent ? (
                    <BreadcrumbPage>{item.title}</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink href={item.href} asChild={!!item.linkComponent}>
                      {item.linkComponent ? (
                        <item.linkComponent href={item.href}>{item.title}</item.linkComponent>
                      ) : (
                        item.title
                      )}
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
              </div>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      )}

      {/* 返回按钮 */}
      {showBackButton && !showBreadcrumbs && (
        <Button variant="ghost" size="sm" className="mb-4 h-auto py-1 pr-3 pl-2" asChild>
          <a href={backHref}>
            <ChevronLeft className="mr-1 h-4 w-4" />
            返回
          </a>
        </Button>
      )}

      {/* 标题和操作按钮 */}
      {title && (
        <div className="mb-6 flex items-center justify-between">
          <h1 className="text-lg font-medium">{title}</h1>
          {action}
        </div>
      )}

      {/* 内容 */}
      {children}
    </div>
  );
}

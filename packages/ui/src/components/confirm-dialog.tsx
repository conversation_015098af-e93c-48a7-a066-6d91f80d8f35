"use client";

import { ReactNode, useEffect, useRef } from "react";
import { Button } from "./button.tsx";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "./dialog.tsx";

interface ConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string | ReactNode;
  onConfirm: (e: React.MouseEvent) => void;
  onCancel?: () => void;
  confirmText?: string;
  cancelText?: string;
  variant?: "default" | "destructive";
  loading?: boolean;
  disabled?: boolean;
}

export const ConfirmDialog = ({
  open,
  onOpenChange,
  title,
  description,
  onConfirm,
  onCancel,
  confirmText = "确认",
  cancelText = "取消",
  variant = "default",
  loading = false,
  disabled = false,
}: ConfirmDialogProps) => {
  const firstButtonRef = useRef<HTMLButtonElement>(null);

  // 当对话框打开时，聚焦到第一个按钮以确保焦点转移
  useEffect(() => {
    if (open && firstButtonRef.current) {
      // 使用 setTimeout 确保在 DOM 更新后聚焦
      setTimeout(() => {
        firstButtonRef.current?.focus();
      }, 0);
    }
  }, [open]);

  const handleCancel = () => {
    onCancel?.();
    onOpenChange(false);
  };

  const handleConfirm = (e: React.MouseEvent) => {
    onConfirm(e);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader className="pb-0">
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            ref={firstButtonRef}
            variant="outline"
            onClick={handleCancel}
            disabled={disabled || loading}
          >
            {cancelText}
          </Button>
          <Button
            variant={variant === "destructive" ? "destructive" : "default"}
            onClick={handleConfirm}
            disabled={disabled || loading}
          >
            {loading ? "处理中..." : confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

"use client";

import * as React from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@ragtop-web/ui/components/sonner";

/**
 * 默认的查询客户端配置
 */
const defaultOptions = {
  queries: {
    refetchOnWindowFocus: false,
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5分钟
  },
};

/**
 * 查询提供者组件
 *
 * 提供React Query功能和全局错误提示
 */
export function QueryProvider({
  children,
  options = defaultOptions,
}: {
  children: React.ReactNode;
  options?: any;
}) {
  // 创建一个React Query客户端实例
  const [queryClient] = React.useState(() => new QueryClient({ defaultOptions: options }));

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <Toaster position="top-right" />
    </QueryClientProvider>
  );
}

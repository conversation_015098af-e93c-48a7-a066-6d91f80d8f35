"use client";

import { useEffect, useRef } from "react";
import type { EmbedOptions, Result, VisualizationSpec } from "vega-embed";

import { colorScheme, embedOptions } from "./embed-options.ts";
import { enhanceBarChartSpacing } from "./utils/bar.ts";
import { hideLegend } from "./utils/legend.ts";

/**
 * Props for the VegaLiteChart component.
 */
interface VegaLiteChartProps {
  showTitle?: boolean;
  /**
   * The Vega-Lite specification object that defines the chart.
   * @see https://vega.github.io/vega-lite/docs/spec.html
   */
  spec: VisualizationSpec;

  /**
   * Optional configuration options for vegaEmbed.
   * Allows customization of aspects like actions menu, theme, etc.
   * @see https://github.com/vega/vega-embed#options
   */
  options?: EmbedOptions;

  /**
   * Optional flag to force refresh the CSS variable cache.
   * Set this to a different value (e.g. increment a counter) when you want to force refresh.
   */
  refreshCssVarCache?: number;

  /**
   * Optional callback function invoked when the chart is successfully embedded.
   * Receives the vega-embed Result object which contains the Vega View instance.
   * @param result - The result object from vegaEmbed.
   */
  onEmbedSuccess?: (result: Result) => void;

  /**
   * Optional callback function invoked if there's an error during embedding.
   * @param error - The error object.
   */
  onEmbedError?: (error: Error) => void;

  /**
   * Optional CSS class name to apply to the container div.
   */
  className?: string;

  /**
   * Optional inline styles to apply to the container div.
   */
  style?: React.CSSProperties;
}

/**
 * A reusable React component to render Vega-Lite charts using vega-embed.
 * It handles the lifecycle of the Vega view, including creation and cleanup.
 */
const VegaLiteChart: React.FC<VegaLiteChartProps> = ({
  spec,
  options = {},
  onEmbedSuccess,
  onEmbedError,
  className,
  style,
}) => {
  // Ref for the container element where the chart will be rendered.
  const chartContainerRef = useRef<HTMLDivElement>(null);
  // Ref to store the vega-embed result, which includes the Vega View instance.
  const embedResultRef = useRef<Result | null>(null);

  useEffect(() => {
    // 在effect开始时保存ref的当前值，以便在cleanup函数中使用
    const chartContainer = chartContainerRef.current;

    // Ensure the container ref is available and a spec is provided.
    if (chartContainer && spec) {
      // 动态导入 vega-embed 以避免服务器端序列化问题
      import("vega-embed")
        .then(({ default: vegaEmbed, vega }) => {
          // 注册自定义颜色方案
          vega.scheme("customBlue", colorScheme);

          const finalSpec = {
            ...enhanceBarChartSpacing(hideLegend(spec)),
            // 设置图表的宽度和高度为容器的大小
            width: "container",
            height: "container",
            autosize: {
              type: "fit",
              contains: "padding",
            },
            title: "",
          } as VisualizationSpec;

          const rawEmbedOptions = {
            ...embedOptions,
            ...options,
          };

          // Use vegaEmbed to render the spec into the container div.
          vegaEmbed(chartContainer, finalSpec, rawEmbedOptions)
            .then((result: Result) => {
              // Store the result object, which contains the view.
              embedResultRef.current = result;
              // Call the success callback if provided.
              onEmbedSuccess?.(result);
            })
            .catch((error: Error) => {
              console.error("Error embedding Vega-Lite chart:", error);
              // Call the error callback if provided.
              onEmbedError?.(error);
            });
        })
        .catch((error) => {
          console.error("Error loading vega-embed:", error);
          onEmbedError?.(error as Error);
        });
    }

    // Cleanup function: runs when component unmounts or dependencies change.
    return () => {
      if (embedResultRef.current) {
        // Properly finalize the Vega view to release resources and event listeners.
        try {
          embedResultRef.current.view.finalize();
        } catch (error) {
          // Finalize might throw if view is already removed/invalid
          console.warn("Error during Vega view finalization:", error);
        }
        embedResultRef.current = null;
      }
      // Optional: Explicitly clear the container's inner HTML as an extra precaution.
      if (chartContainer) {
        chartContainer.innerHTML = "";
      }
    };
  }, [spec, onEmbedSuccess, onEmbedError, options]);

  return (
    <div className="flex h-full w-full flex-col gap-2 overflow-hidden rounded-xl border border-zinc-800">
      <div ref={chartContainerRef} className={className} style={style} />
    </div>
  );
};

export default VegaLiteChart;

"use client";

import { VisualizationSpec } from "vega-embed";

/**
 * 获取数据长度的工具函数，支持多种数据源
 * @param spec - Vega-Lite规范对象
 * @returns 数据长度，如果无法确定则返回Infinity
 */
const getDataLength = (spec: VisualizationSpec): number => {
  // 情况1: 直接使用 data.values
  if (spec.data && "values" in spec.data && Array.isArray(spec.data.values)) {
    return spec.data.values.length;
  }

  // 情况2: 使用 datasets
  if ("datasets" in spec && spec.datasets && spec.data && "name" in spec.data && spec.data.name) {
    const datasetName = spec.data.name;
    const datasets = spec.datasets as Record<string, unknown>;
    const dataset = datasets[datasetName];
    if (dataset && Array.isArray(dataset)) {
      return dataset.length;
    }
  }

  // 情况3: 其他情况（如URL数据）返回一个足够大的值，不应用宽度限制
  return Infinity;
};

/**
 * 自动为柱状图添加间距配置的工具函数
 * 检测图表类型，只对柱状图添加paddingInner和paddingOuter
 */
export const enhanceBarChartSpacing = (spec: VisualizationSpec): VisualizationSpec => {
  // 深拷贝spec避免修改原对象
  const enhancedSpec = JSON.parse(JSON.stringify(spec));

  // 检查是否是柱状图（mark为"bar"或mark.type为"bar"）
  const isBarChart =
    enhancedSpec.mark === "bar" ||
    (typeof enhancedSpec.mark === "object" && enhancedSpec.mark?.type === "bar");

  if (isBarChart) {
    // 只有当x轴存在且没有已配置的scale时才添加
    if (!enhancedSpec.encoding?.x?.scale) {
      enhancedSpec.encoding.x.scale = {};
    }

    // 只添加间距配置，不覆盖已有的scale配置
    if (!enhancedSpec.encoding?.x?.scale?.paddingInner) {
      enhancedSpec.encoding.x.scale.paddingInner = 0.2;
    }
    if (!enhancedSpec.encoding?.x?.scale?.paddingOuter) {
      enhancedSpec.encoding.x.scale.paddingOuter = 0.05;
    }

    // 使用新的getDataLength函数来检查数据长度
    if (getDataLength(enhancedSpec) < 5) {
      const mark =
        typeof enhancedSpec.mark === "string" ? { type: enhancedSpec.mark } : enhancedSpec.mark;
      enhancedSpec.mark = {
        ...mark,
        width: 40,
      };
    }
  }

  return enhancedSpec;
};

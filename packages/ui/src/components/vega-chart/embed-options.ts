import { EmbedOptions } from "vega-embed";

export const colorScheme = [
  "#C1DCFC", // 淡蓝白（起始）
  "#ADCFFB", // 晴空蓝
  "#87B5F9", // 雾蓝
  "#5C9BF7", // 亮蓝
  "#3B82F6", // 鲜明蓝（中间色）
  "#286CE1", // 饱和蓝
  "#1B4D93", // 钢蓝
  "#102C63", // 深蓝灰
  "#07183F", // 深靛蓝（终结）
];

export const embedOptions: EmbedOptions = {
  actions: {
    export: true, // export as PNG/SVG
    source: false, // view source
    compiled: false, // view compiled Vega
    editor: false, // open in Vega Editor
  },
  // theme: "dark",
  padding: 10,
  // tooltip: {
  //   theme: "dark",
  // },
  config: {
    background: "#ffffff",
    title: {
      fontSize: 18,
      font: "Inter, Helvetica, Arial, sans-serif",
      color: "#222",
      anchor: "start",
      fontWeight: "bold",
      offset: 20,
    },
    axis: {
      domain: true,
      domainColor: "#888",
      domainWidth: 1,
      grid: true,
      gridColor: "#e5e7eb",
      gridWidth: 1,
      labelFontSize: 14,
      labelColor: "#444",
      tickColor: "#bbb",
      tickSize: 6,
      titleFontSize: 14,
      titleFontWeight: "bold",
      titleColor: "#666",
    },
    line: {
      color: "#2563eb",
      strokeWidth: 3,
    },
    view: {
      stroke: "transparent",
    },
  },
};

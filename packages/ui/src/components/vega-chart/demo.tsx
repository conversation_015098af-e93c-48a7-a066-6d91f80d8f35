"use client";

import { useState } from "react";
import { VisualizationSpec } from "vega-embed";

import VegaLiteChart from "./lite-vega-chart.tsx";

export default function VegaTestPage() {
  // 选定可视化
  const [selectedVisId, setSelectedVisId] = useState<string | null>(null);

  // 所有图表的规格对象集合
  const visualizations: Record<string, { title: string; spec: VisualizationSpec }> = {
    // 基础柱状图
    barChart1: {
      title: "基础柱状图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        data: {
          name: "data-4537639e3f87cc6bc5b5c337e4f3b851",
        },
        datasets: {
          "data-4537639e3f87cc6bc5b5c337e4f3b851": [
            {
              "Phone Number": "182****5635",
              "Voucher Count": 1,
            },
            {
              "Phone Number": "182****9129",
              "Voucher Count": 720,
            },
            {
              "Phone Number": "156****1951",
              "Voucher Count": 581,
            },
            {
              "Phone Number": "185****3982",
              "Voucher Count": 513,
            },
            {
              "Phone Number": "158****7713",
              "Voucher Count": 502,
            },
            {
              "Phone Number": "133****7289",
              "Voucher Count": 450,
            },
            {
              "Phone Number": "137****0619",
              "Voucher Count": 443,
            },
            {
              "Phone Number": "182****3093",
              "Voucher Count": 442,
            },
            {
              "Phone Number": "180****3323",
              "Voucher Count": 439,
            },
            {
              "Phone Number": "187****2786",
              "Voucher Count": 435,
            },
          ],
        },
        encoding: {
          color: {
            field: "Voucher Count",
            legend: {
              title: "Voucher Count",
            },
            type: "quantitative",
          },
          tooltip: [
            {
              field: "Phone Number",
              type: "nominal",
            },
            {
              field: "Voucher Count",
              type: "quantitative",
            },
          ],
          x: {
            field: "Phone Number",
            title: "Phone Number",
            type: "ordinal",
          },
          y: {
            field: "Voucher Count",
            title: "Voucher Count",
            type: "quantitative",
          },
        },
        mark: {
          type: "bar",
        },
        title: "Top 10 Individuals by Voucher Usage in 2024",
      },
    },

    // 基础柱状图
    barChart: {
      title: "基础柱状图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "A simple bar chart",
        data: {
          values: [
            { category: "A", value: 28 },
            { category: "B", value: 55 },
            { category: "C", value: 43 },
            { category: "D", value: 91 },
            { category: "E", value: 81 },
            { category: "F", value: 53 },
            { category: "G", value: 19 },
            { category: "H", value: 87 },
          ],
        },
        mark: "bar",
        encoding: {
          x: { field: "category", type: "nominal" },
          y: { field: "value", type: "quantitative" },
          color: { field: "category", type: "nominal", legend: null },
        },
      },
    },

    // 分组柱状图
    groupedBarChart: {
      title: "分组柱状图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "A grouped bar chart",
        data: {
          values: [
            { category: "A", group: "Group 1", value: 28 },
            { category: "A", group: "Group 2", value: 55 },
            { category: "B", group: "Group 1", value: 43 },
            { category: "B", group: "Group 2", value: 91 },
            { category: "C", group: "Group 1", value: 81 },
            { category: "C", group: "Group 2", value: 53 },
            { category: "D", group: "Group 1", value: 19 },
            { category: "D", group: "Group 2", value: 87 },
          ],
        },
        mark: "bar",
        encoding: {
          x: {
            field: "category",
            type: "nominal",
            title: "类别",
          },
          y: { field: "value", type: "quantitative", title: "数值" },
          xOffset: {
            field: "group",
            type: "nominal",
            // scale: { paddingInner: 0.1 }, // 设置偏移量
          },
          color: { field: "group", type: "nominal" },
        },
      },
    },

    // 堆叠柱状图
    stackedBarChart: {
      title: "堆叠柱状图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "A stacked bar chart",
        data: {
          values: [
            { category: "A", group: "Group 1", value: 28 },
            { category: "A", group: "Group 2", value: 55 },
            { category: "B", group: "Group 1", value: 43 },
            { category: "B", group: "Group 2", value: 91 },
            { category: "C", group: "Group 1", value: 81 },
            { category: "C", group: "Group 2", value: 53 },
            { category: "D", group: "Group 1", value: 19 },
            { category: "D", group: "Group 2", value: 87 },
          ],
        },
        mark: "bar",
        encoding: {
          x: { field: "category", type: "nominal", title: "类别" },
          y: {
            field: "value",
            type: "quantitative",
            title: "数值",
            stack: "normalize",
          },
          color: { field: "group", type: "nominal" },
        },
      },
    },

    // 基础折线图
    lineChart: {
      title: "基础折线图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "A simple line chart",
        data: {
          values: [
            { x: 0, y: 28 },
            { x: 1, y: 43 },
            { x: 2, y: 81 },
            { x: 3, y: 56 },
            { x: 4, y: 38 },
            { x: 5, y: 73 },
            { x: 6, y: 62 },
            { x: 7, y: 54 },
          ],
        },
        mark: {
          type: "line",
          point: true,
        },
        encoding: {
          x: { field: "x", type: "quantitative", title: "X轴" },
          y: { field: "y", type: "quantitative", title: "Y轴" },
        },
      },
    },

    // 多条折线图
    multiLineChart: {
      title: "多条折线图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Multiple line series chart",
        data: {
          values: [
            { month: "Jan", series: "Series 1", value: 28 },
            { month: "Feb", series: "Series 1", value: 55 },
            { month: "Mar", series: "Series 1", value: 43 },
            { month: "Apr", series: "Series 1", value: 91 },
            { month: "May", series: "Series 1", value: 81 },
            { month: "Jun", series: "Series 1", value: 53 },
            { month: "Jan", series: "Series 2", value: 60 },
            { month: "Feb", series: "Series 2", value: 49 },
            { month: "Mar", series: "Series 2", value: 72 },
            { month: "Apr", series: "Series 2", value: 50 },
            { month: "May", series: "Series 2", value: 35 },
            { month: "Jun", series: "Series 2", value: 80 },
          ],
        },
        mark: {
          type: "line",
          point: true,
        },
        encoding: {
          x: { field: "month", type: "ordinal", title: "月份" },
          y: { field: "value", type: "quantitative", title: "数值" },
          color: { field: "series", type: "nominal" },
        },
      },
    },

    // 面积图
    areaChart: {
      title: "面积图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Area chart",
        data: {
          values: [
            { month: "Jan", value: 28 },
            { month: "Feb", value: 55 },
            { month: "Mar", value: 43 },
            { month: "Apr", value: 91 },
            { month: "May", value: 81 },
            { month: "Jun", value: 53 },
            { month: "Jul", value: 19 },
            { month: "Aug", value: 87 },
          ],
        },
        mark: "area",
        encoding: {
          x: { field: "month", type: "ordinal", title: "月份" },
          y: { field: "value", type: "quantitative", title: "数值" },
        },
      },
    },

    // 堆叠面积图
    stackedAreaChart: {
      title: "堆叠面积图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Stacked area chart",
        data: {
          values: [
            { month: "Jan", series: "Series 1", value: 28 },
            { month: "Feb", series: "Series 1", value: 55 },
            { month: "Mar", series: "Series 1", value: 43 },
            { month: "Apr", series: "Series 1", value: 91 },
            { month: "May", series: "Series 1", value: 81 },
            { month: "Jun", series: "Series 1", value: 53 },
            { month: "Jan", series: "Series 2", value: 60 },
            { month: "Feb", series: "Series 2", value: 49 },
            { month: "Mar", series: "Series 2", value: 72 },
            { month: "Apr", series: "Series 2", value: 50 },
            { month: "May", series: "Series 2", value: 35 },
            { month: "Jun", series: "Series 2", value: 80 },
          ],
        },
        mark: "area",
        encoding: {
          x: { field: "month", type: "ordinal", title: "月份" },
          y: {
            field: "value",
            type: "quantitative",
            title: "数值",
            stack: "zero",
          },
          color: { field: "series", type: "nominal" },
        },
      },
    },

    // 饼图
    pieChart: {
      title: "饼图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Pie chart",
        data: {
          values: [
            { category: "A", value: 28 },
            { category: "B", value: 55 },
            { category: "C", value: 43 },
            { category: "D", value: 91 },
            { category: "E", value: 81 },
          ],
        },
        mark: "arc",
        encoding: {
          theta: { field: "value", type: "quantitative" },
          color: { field: "category", type: "nominal" },
        },
        view: { stroke: null },
      },
    },

    // 环形图
    donutChart: {
      title: "环形图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Donut chart",
        data: {
          values: [
            { category: "A", value: 28 },
            { category: "B", value: 55 },
            { category: "C", value: 43 },
            { category: "D", value: 91 },
            { category: "E", value: 81 },
          ],
        },
        mark: { type: "arc", innerRadius: 70 },
        encoding: {
          theta: { field: "value", type: "quantitative" },
          color: { field: "category", type: "nominal" },
        },
        view: { stroke: null },
      },
    },

    // 散点图
    scatterPlot: {
      title: "散点图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Scatter plot",
        data: {
          values: Array.from({ length: 50 }, () => ({
            x: Math.random() * 100,
            y: Math.random() * 100,
            size: Math.random() * 20 + 5,
            category: ["A", "B", "C", "D"][Math.floor(Math.random() * 4)],
          })),
        },
        mark: "point",
        encoding: {
          x: { field: "x", type: "quantitative", title: "X轴" },
          y: { field: "y", type: "quantitative", title: "Y轴" },
          size: { field: "size", type: "quantitative" },
          color: { field: "category", type: "nominal" },
        },
      },
    },

    // 气泡图
    bubbleChart: {
      title: "气泡图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Bubble chart",
        data: {
          values: Array.from({ length: 20 }, (_, i) => ({
            x: i % 5,
            y: Math.floor(i / 5),
            size: Math.random() * 1000 + 100,
            category: ["A", "B", "C", "D"][Math.floor(Math.random() * 4)],
          })),
        },
        mark: "circle",
        encoding: {
          x: { field: "x", type: "ordinal", title: "X轴" },
          y: { field: "y", type: "ordinal", title: "Y轴" },
          size: {
            field: "size",
            type: "quantitative",
            scale: { range: [100, 1000] },
          },
          color: { field: "category", type: "nominal" },
        },
      },
    },

    // 热力图
    heatmap: {
      title: "热力图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Heatmap",
        data: {
          values: Array.from({ length: 25 }, (_, i) => ({
            x: (i % 5) + 1,
            y: Math.floor(i / 5) + 1,
            heat: Math.random() * 100,
          })),
        },
        mark: "rect",
        encoding: {
          x: { field: "x", type: "ordinal", title: "X轴" },
          y: { field: "y", type: "ordinal", title: "Y轴" },
          color: {
            field: "heat",
            type: "quantitative",
            // scale: { scheme: "viridis" },
            legend: { title: "热度" },
          },
        },
      },
    },

    // 径向图（替代雷达图）
    radialChart: {
      title: "径向图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Radial chart",
        data: {
          values: [
            { category: "Strength", value: 80 },
            { category: "Intelligence", value: 70 },
            { category: "Charisma", value: 50 },
            { category: "Dexterity", value: 60 },
            { category: "Luck", value: 40 },
            { category: "Magic", value: 90 },
          ],
        },
        encoding: {
          theta: {
            field: "category",
            type: "nominal",
            scale: { range: [0, 6.28] }, // 2*PI
          },
          radius: {
            field: "value",
            type: "quantitative",
            scale: { type: "sqrt", zero: true, range: [0, 180] },
          },
          color: { field: "category", type: "nominal" },
        },
        layer: [
          {
            mark: { type: "arc", innerRadius: 20, stroke: "#fff" },
          },
          {
            mark: { type: "text", radiusOffset: 15 },
            encoding: {
              text: { field: "value", type: "quantitative" },
            },
          },
          {
            mark: { type: "text", radiusOffset: 45 },
            encoding: {
              text: { field: "category", type: "nominal" },
              radius: { value: 190 },
            },
          },
        ],
      },
    },

    // 地图(美国各州)
    usMap: {
      title: "美国各州地图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "US map",
        data: {
          url: "https://vega.github.io/vega-datasets/data/us-10m.json",
          format: {
            type: "topojson",
            feature: "states",
          },
        },
        transform: [
          {
            lookup: "id",
            from: {
              data: {
                values: Array.from({ length: 51 }, (_, i) => ({
                  id: i,
                  rate: Math.random() * 100,
                })),
              },
              key: "id",
              fields: ["rate"],
            },
          },
        ],
        projection: { type: "albersUsa" },
        mark: { type: "geoshape" },
        encoding: {
          color: {
            field: "rate",
            type: "quantitative",
            // scale: { scheme: "blues" },
          },
          tooltip: [
            { field: "id", type: "nominal", title: "State ID" },
            { field: "rate", type: "quantitative", title: "Rate" },
          ],
        },
      },
    },

    // 箱线图
    boxPlot: {
      title: "箱线图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Box plot",
        data: {
          values: [
            ...Array.from({ length: 30 }, () => ({
              category: "A",
              value: Math.random() * 100,
            })),
            ...Array.from({ length: 30 }, () => ({
              category: "B",
              value: 20 + Math.random() * 80,
            })),
            ...Array.from({ length: 30 }, () => ({
              category: "C",
              value: 50 + Math.random() * 50,
            })),
          ],
        },
        mark: {
          type: "boxplot",
          extent: "min-max",
        },
        encoding: {
          x: { field: "category", type: "nominal", title: "类别" },
          y: { field: "value", type: "quantitative", title: "数值" },
          color: { field: "category", type: "nominal", legend: null },
        },
      },
    },

    // 瀑布图
    waterfallChart: {
      title: "瀑布图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Waterfall chart",
        data: {
          values: [
            {
              category: "Beginning",
              delta: 0,
              amount: 23000,
              previous: 0,
              positive: true,
            },
            {
              category: "Income 1",
              delta: 4000,
              amount: 27000,
              previous: 23000,
              positive: true,
            },
            {
              category: "Income 2",
              delta: 1500,
              amount: 28500,
              previous: 27000,
              positive: true,
            },
            {
              category: "Expense 1",
              delta: -2000,
              amount: 26500,
              previous: 28500,
              positive: false,
            },
            {
              category: "Expense 2",
              delta: -1000,
              amount: 25500,
              previous: 26500,
              positive: false,
            },
            {
              category: "Income 3",
              delta: 6000,
              amount: 31500,
              previous: 25500,
              positive: true,
            },
            {
              category: "Ending",
              delta: 0,
              amount: 31500,
              previous: 31500,
              positive: true,
            },
          ],
        },
        layer: [
          {
            mark: { type: "bar", size: 40 },
            encoding: {
              x: {
                field: "category",
                type: "ordinal",
                title: "类别",
                sort: null,
              },
              y: { field: "previous", type: "quantitative", title: "金额" },
              y2: { field: "amount" },
              color: {
                field: "positive",
                type: "nominal",
                scale: { range: ["#FF7272", "#67B35C"] },
                legend: null,
              },
            },
          },
          {
            mark: { type: "rule", xOffset: -20, x2Offset: 20 },
            encoding: {
              x: { field: "category", type: "ordinal" },
              y: { field: "previous", type: "quantitative" },
              x2: { field: "category", type: "ordinal" },
            },
          },
          {
            mark: { type: "rule", xOffset: -20, x2Offset: 20 },
            encoding: {
              x: { field: "category", type: "ordinal" },
              y: { field: "amount", type: "quantitative" },
              x2: { field: "category", type: "ordinal" },
            },
          },
          {
            mark: { type: "text", dy: -8 },
            encoding: {
              x: { field: "category", type: "ordinal" },
              y: { field: "amount", type: "quantitative" },
              text: { field: "amount", type: "quantitative" },
            },
          },
        ],
      },
    },

    // 树形图
    treemap: {
      title: "树形图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Treemap",
        data: {
          values: [
            { id: 1, parent: null, name: "Root", size: null },
            { id: 2, parent: 1, name: "Branch 1", size: null },
            { id: 3, parent: 1, name: "Branch 2", size: null },
            { id: 4, parent: 2, name: "Leaf 1.1", size: 100 },
            { id: 5, parent: 2, name: "Leaf 1.2", size: 200 },
            { id: 6, parent: 3, name: "Leaf 2.1", size: 150 },
            { id: 7, parent: 3, name: "Leaf 2.2", size: 300 },
          ],
        },
        transform: [
          {
            filter: "datum.size !== null",
          },
        ],
        mark: "rect",
        encoding: {
          color: {
            field: "name",
            type: "nominal",
          },
          size: {
            field: "size",
            type: "quantitative",
          },
          tooltip: [
            { field: "name", type: "nominal" },
            { field: "size", type: "quantitative" },
          ],
        },
      },
    },

    // 流数据动画图
    streamingChart: {
      title: "流数据动画",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Example of streaming data animation",
        data: {
          name: "table",
          values: Array.from({ length: 20 }, (_, i) => ({
            x: i,
            y: Math.sin(i / 3) * 10 + 20 + Math.random() * 5,
            category: 0,
          })),
        },
        mark: {
          type: "line",
          point: true,
        },
        encoding: {
          x: {
            field: "x",
            type: "quantitative",
            scale: { zero: false },
          },
          y: {
            field: "y",
            type: "quantitative",
            scale: { domain: [0, 40] },
          },
          color: { field: "category", type: "nominal" },
        },
      },
    },

    // 时间序列图
    timeSeriesChart: {
      title: "时间序列图",
      spec: {
        $schema: "https://vega.github.io/schema/vega-lite/v6.json",
        description: "Time series chart with interval selection",
        data: {
          values: Array.from({ length: 100 }, (_, i) => ({
            date: new Date(2022, 0, i + 1).toISOString().split("T")[0],
            price: 100 + Math.random() * 50 + (i % 30) * 2 + Math.sin(i / 10) * 20,
          })),
        },
        mark: "line",
        encoding: {
          x: {
            field: "date",
            type: "temporal",
            title: "日期",
            axis: { format: "%b %d" },
          },
          y: {
            field: "price",
            type: "quantitative",
            title: "价格",
          },
        },
        params: [
          {
            name: "brush",
            select: {
              type: "interval",
              encodings: ["x"],
            },
          },
        ],
        width: "container",
        height: "container",
      },
    },
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-6 text-2xl font-bold">Vega-Lite v6 图表测试页面</h1>

      {/* 选中的单个图表视图 */}
      {selectedVisId && (
        <div className="mb-8">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold">{visualizations[selectedVisId].title}</h2>
            <button
              className="rounded-md bg-blue-600 px-3 py-1 text-sm text-white"
              onClick={() => setSelectedVisId(null)}
            >
              返回全部图表
            </button>
          </div>
          <div className="h-[600px] rounded-lg border border-gray-200 p-4">
            <VegaLiteChart
              spec={{
                ...visualizations[selectedVisId].spec,
                autosize: {
                  type: "fit",
                  contains: "padding",
                },
              }}
              className="h-full w-full"
            />
          </div>
        </div>
      )}

      {/* 图表网格布局 */}
      {!selectedVisId && (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Object.entries(visualizations).map(([id, { title, spec }]) => (
            <div
              key={id}
              className="cursor-pointer rounded-lg border border-gray-200 p-4 transition hover:shadow-md"
              onClick={() => setSelectedVisId(id)}
            >
              <h3 className="mb-2 text-lg font-medium">{title}</h3>
              <div className="h-[250px]">
                <VegaLiteChart spec={spec} className="h-full w-full" />
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

{"name": "@ragtop-web/eslint-config", "version": "0.0.0", "private": true, "type": "module", "main": "index.js", "files": ["index.js", "next.js", "react.js", "typescript.js"], "dependencies": {"@eslint/js": "^9.0.0", "eslint-config-next": "^14.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "typescript-eslint": "^7.0.0"}, "peerDependencies": {"eslint": "^9.0.0", "typescript": "^5.0.0"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@next/eslint-plugin-next": "15.3.5", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-better-tailwindcss": "3.4.4", "eslint-plugin-only-warn": "1.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-turbo": "2.5.4", "eslint-plugin-unicorn": "59.0.1", "eslint-plugin-unused-imports": "^3.2.0", "globals": "16.3.0", "prettier": "^3.5.1"}}
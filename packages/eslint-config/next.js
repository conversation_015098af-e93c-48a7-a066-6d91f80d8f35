import js from "@eslint/js";
import pluginNext from "@next/eslint-plugin-next";
import eslintConfigPrettier from "eslint-config-prettier";
import pluginReact from "eslint-plugin-react";
import pluginReactHooks from "eslint-plugin-react-hooks";
import globals from "globals";
import tseslint from "typescript-eslint";

import { config as baseConfig } from "./base.js";

/**
 * A custom ESLint configuration for libraries that use Next.js.
 *
 * @type {import("eslint").Linter.Config}
 * */
export const nextJsConfig = [
  ...baseConfig,
  js.configs.recommended,
  eslintConfigPrettier,
  ...tseslint.configs.recommended,
  {
    ...pluginReact.configs.flat.recommended,
    languageOptions: {
      ...pluginReact.configs.flat.recommended.languageOptions,
      globals: {
        ...globals.serviceworker,
      },
    },
  },
  {
    plugins: {
      "@next/next": pluginNext,
    },
    rules: {
      ...pluginNext.configs.recommended.rules,
      ...pluginNext.configs["core-web-vitals"].rules,
    },
  },
  {
    plugins: {
      "react-hooks": pluginReactHooks,
    },
    settings: { react: { version: "detect" } },
    rules: {
      ...pluginReactHooks.configs.recommended.rules,
      // React scope no longer necessary with new JSX transform.
      "react/react-in-jsx-scope": "off",
      "react/prop-types": "off",
    },
  },
];

module.exports = {
  extends: ["./index.js", "next/core-web-vitals"],
  rules: {
    // Next.js specific rules
    "@next/next/no-html-link-for-pages": "error",
    "@next/next/no-img-element": "error",
    "@next/next/no-unwanted-polyfillio": "error",
    "@next/next/no-page-custom-font": "error",
    "@next/next/no-sync-scripts": "error",
    "@next/next/no-title-in-document-head": "error",
    "@next/next/google-font-display": "error",
    "@next/next/google-font-preconnect": "error",
    "@next/next/inline-script-id": "error",
    "@next/next/next-script-for-ga": "error",
    "@next/next/no-css-tags": "error",
    "@next/next/no-head-import-in-page": "error",
    "@next/next/no-typos": "error",
    "@next/next/no-unwanted-polyfillio": "error",
    "@next/next/no-duplicate-head": "error",
    "@next/next/no-document-import-in-page": "error",
    "@next/next/no-head-import-in-page": "error",
    "@next/next/no-img-element": "error",
    "@next/next/no-page-custom-font": "error",
    "@next/next/no-sync-scripts": "error",
    "@next/next/no-title-in-document-head": "error",
    "@next/next/google-font-display": "error",
    "@next/next/google-font-preconnect": "error",
    "@next/next/inline-script-id": "error",
    "@next/next/next-script-for-ga": "error",
    "@next/next/no-css-tags": "error",
    "@next/next/no-head-import-in-page": "error",
    "@next/next/no-typos": "error",
    "@next/next/no-unwanted-polyfillio": "error",
    "@next/next/no-duplicate-head": "error",
    "@next/next/no-document-import-in-page": "error",
  },
};
